'use client'

import { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { usePathname } from 'next/navigation'
import { isCrew } from '@/app/helpers/userHelper'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { Combobox } from '@/components/ui/comboBox'
import { ReadSeaLogsMembers } from './queries'

const CrewDropdown = ({
    label = 'Trainer',
    value,
    onChange,
    controlClasses = 'default' as 'default' | 'filter',
    placeholder = 'Trainer',
    isClearable = false,
    filterByTrainingSessionMemberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
    multi = false,
    offline = false,
    vesselID = 0,
    disabled = false,
}: any) => {
    const [crewList, setCrewList] = useState<any>()
    const [selectedValue, setSelectedValue] = useState<any>(multi ? [] : null)

    // Debug selectedValue changes

    const [isLoading, setIsLoading] = useState(true)
    const [allCrewList, setAllCrewList] = useState<any>([])
    const pathname = usePathname()
    const [imCrew, setImCrew] = useState(false) // if this is a crew member or not
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const [allFetchedData, setAllFetchedData] = useState<any[]>([])
    const [currentFilter, setCurrentFilter] = useState<any>({})
    const [currentOffset, setCurrentOffset] = useState(0)

    const processCompleteData = (completeData: any[]) => {
        // If vesselID > 0, filter the crew list to display only the vessel crew.
        const vesselCrewList =
            vesselID > 0
                ? completeData.filter((crew: any) =>
                      crew.vehicles.nodes.some(
                          (vehicle: any) => +vehicle.id === vesselID,
                      ),
                  )
                : completeData
        if (vesselCrewList) {
            if (imCrew && pathname === '/reporting') {
                // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user
                const userId = localStorage.getItem('userId')
                const filteredCrewList = vesselCrewList.filter((crew: any) => {
                    return crew.id === userId
                })
                setCrewList(filteredCrewList)
            } else {
                setCrewList(vesselCrewList)
            }

            setAllCrewList(vesselCrewList)
        }
    }

    const [querySeaLogsMembersList] = useLazyQuery(ReadSeaLogsMembers, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            const pageInfo = response.readSeaLogsMembers.pageInfo

            // Accumulate data from all pages
            const newCompleteData = [...allFetchedData, ...data]
            setAllFetchedData(newCompleteData)

            // If there are more pages, fetch the next page
            if (pageInfo.hasNextPage) {
                const newOffset = currentOffset + data.length
                setCurrentOffset(newOffset)
                querySeaLogsMembersList({
                    variables: {
                        filter: currentFilter,
                        offset: newOffset,
                        limit: 100,
                    },
                })
                return // Don't process the crew list yet, wait for all data
            }

            // All data has been fetched, now process the complete dataset
            processCompleteData(newCompleteData)
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembersList error', error)
        },
    })
    const loadCrewMembers = async () => {
        let filter: any = { isArchived: { eq: false } }
        if (filterByTrainingSessionMemberId > 0) {
            filter = {
                ...filter,
                trainingSessions: {
                    members: {
                        id: { contains: filterByTrainingSessionMemberId },
                    },
                },
            }
        }
        if (offline) {
            // querySeaLogsMembersList
            const allCrews = await seaLogsMemberModel.getAll()
            const data = allCrews.filter((crew: any) => {
                if (filterByTrainingSessionMemberId > 0) {
                    return (
                        crew.isArchived === false &&
                        crew.trainingSessions.nodes.some(
                            (trainingSession: any) =>
                                trainingSession.members.nodes.some(
                                    (member: any) =>
                                        member.id ===
                                        filterByTrainingSessionMemberId,
                                ),
                        )
                    )
                } else {
                    return crew.isArchived === false
                }
            })
            if (data) {
                if (imCrew && pathname === '/reporting') {
                    // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user
                    const userId = localStorage.getItem('userId')
                    const filteredCrewList = data.filter((crew: any) => {
                        return crew.id === userId
                    })
                    setCrewList(filteredCrewList)
                } else {
                    setCrewList(data)
                }
                setAllCrewList(data)
            }
        } else {
            // Reset accumulated data and set current filter for pagination
            setAllFetchedData([])
            setCurrentOffset(0)
            setCurrentFilter(filter)
            await querySeaLogsMembersList({
                variables: {
                    filter: filter,
                    offset: 0,
                    limit: 100,
                },
            })
        }
    }
    useEffect(() => {
        if (isLoading) {
            setImCrew(isCrew() || false)
            loadCrewMembers()
            setIsLoading(false)
        }
    }, [isLoading])

    useEffect(() => {
        if (value && crewList) {
            const crew = crewList.find((crew: any) => crew.id === value)
            if (crew) {
                const newSelectedValue = {
                    value: crew.id,
                    label: `${crew.firstName || ''} ${crew.surname || ''}`,
                    profile: {
                        firstName: crew.firstName,
                        surname: crew.surname,
                        avatar: null,
                    },
                }

                setSelectedValue(newSelectedValue)
            }
        } else if (!value) {
            // Reset to null when no value is provided to show placeholder

            setSelectedValue(null)
        }
    }, [value, crewList, placeholder])
    useEffect(() => {
        if (trainerIdOptions.length > 0 && allCrewList.length > 0) {
            const filteredCrewList = allCrewList.filter((crew: any) => {
                return trainerIdOptions.includes(crew.id)
            })
            setCrewList(filteredCrewList)
        }
    }, [trainerIdOptions, allCrewList])
    useEffect(() => {
        if (memberIdOptions.length > 0 && allCrewList.length > 0) {
            const filteredCrewList = allCrewList.filter((crew: any) => {
                return memberIdOptions.includes(crew.id)
            })
            setCrewList(filteredCrewList)
        } else if (allCrewList.length > 0) {
            // Use all crew members if no specific memberIdOptions are provided
            setCrewList(allCrewList)
        }
    }, [memberIdOptions, allCrewList, placeholder])

    return (
        <Combobox
            options={crewList?.map((crew: any) => ({
                value: crew.id,
                label: `${crew.firstName || ''} ${crew.surname || ''}`,
                profile: {
                    firstName: crew.firstName,
                    surname: crew.surname,
                    avatar: null,
                },
            }))}
            value={selectedValue} // Use 'value' for controlled component, not 'defaultValues'
            onChange={(selectedOption: any) => {
                // selectedOption is the Option object from Combobox
                setSelectedValue(selectedOption)

                // Debug the data being passed to parent
                const dataToPass = multi
                    ? selectedOption
                    : selectedOption?.value || null

                // Pass the crew data to the parent component
                onChange(dataToPass)
            }}
            //label={label}
            multi={multi}
            //labelClassName="w-full"
            isLoading={!crewList}
            placeholder={placeholder}
            disabled={disabled}
        />
    )
}

export default CrewDropdown
