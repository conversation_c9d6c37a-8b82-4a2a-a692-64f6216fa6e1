'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { SealogsCogIcon } from '@/app/lib/icons'

interface CrewTrainingDropdownProps {
    onChange: (value: any) => void
    overdueList: any
}

export const CrewTrainingFilterActions = ({
    onChange,
    overdueList = false,
}: CrewTrainingDropdownProps) => {
    const { isMobile } = useSidebar()
    const [permissions, setPermissions] = useState<any>(false)
    const [showOverdueList, setShowOverdueList] = useState(overdueList)

    const handleOnChange = () => {
        setShowOverdueList((prev: boolean) => {
            const newValue = !prev
            onChange(newValue)
            // overdue(newValue)
            return newValue
        })
    }

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    useEffect(() => {
        setShowOverdueList(overdueList)
    }, [overdueList])

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                {/* <DropdownMenuItem onClick={() => handleOnChange()}>
                    {showOverdueList
                        ? 'Overdue Trainings'
                        : 'Completed Trainings'}
                </DropdownMenuItem> */}
                <Link href={'/training-type'}>
                    <DropdownMenuItem>
                        Training Schedules / types
                    </DropdownMenuItem>
                </Link>
                {permissions &&
                    hasPermission('RECORD_TRAINING', permissions) && (
                        <Link href={'/crew-training/create'}>
                            <DropdownMenuItem>
                                Record A Training
                            </DropdownMenuItem>
                        </Link>
                    )}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
