'use client'

import { CREW_TRAINING_TYPES } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'

import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'

const TrainingTypeDropdown = ({
    value,
    onChange,
    isClearable = false,
    filterByTrainingSessionMemberId = 0,
    trainingTypeIdOptions = [],
}: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [trainingTypeList, setTrainingTypeList] = useState([] as any)
    const [allTrainingTypeList, setAllTrainingTypeList] = useState([] as any)
    const [selectedTrainingType, setSelectedTrainingType] = useState([] as any)
    const [queryTrainingTypeList, { loading: queryTrainingTypeListLoading }] =
        useLazyQuery(CREW_TRAINING_TYPES, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingTypes.nodes

                if (data) {
                    const formattedData = data.map((trainingType: any) => ({
                        value: trainingType.id,
                        label: trainingType.title,
                    }))
                    formattedData.sort((a: any, b: any) =>
                        a.label.localeCompare(b.label),
                    )

                    setTrainingTypeList(formattedData)
                    setAllTrainingTypeList(formattedData)
                    setSelectedTrainingType(
                        formattedData.find(
                            (trainingType: any) => trainingType.value === value,
                        ),
                    )
                }
            },
            onError: (error: any) => {
                console.error('queryTrainingTypeList error', error)
            },
        })
    const loadTrainingTypeList = async () => {
        let filter = {}
        if (filterByTrainingSessionMemberId > 0) {
            filter = {
                trainingSessions: {
                    members: {
                        id: { contains: filterByTrainingSessionMemberId },
                    },
                },
            }
        }
        queryTrainingTypeList({
            variables: {
                filter: filter,
            },
        })
    }
    useEffect(() => {
        if (isLoading) {
            loadTrainingTypeList()
            setIsLoading(false)
        }
    }, [isLoading])
    useEffect(() => {
        setSelectedTrainingType(
            trainingTypeList.find(
                (trainingType: any) => trainingType.value === value,
            ),
        )
    }, [value])
    useEffect(() => {
        if (
            trainingTypeIdOptions.length > 0 &&
            allTrainingTypeList.length > 0
        ) {
            const trainingTypes = allTrainingTypeList.filter((t: any) =>
                trainingTypeIdOptions.includes(t.value),
            )
            setTrainingTypeList(trainingTypes)
        }
    }, [trainingTypeIdOptions, allTrainingTypeList])

    return (
        <Combobox
            options={trainingTypeList}
            value={selectedTrainingType}
            onChange={(selectedOption: any) => {
                setSelectedTrainingType(selectedOption)
                onChange(selectedOption)
            }}
            isLoading={queryTrainingTypeListLoading}
            title="Training Type"
            placeholder="Training Type"
        />
    )
}

export default TrainingTypeDropdown
