import vesselTypes from "../vesselTypes";
import { LogBookConfiguration } from "./types";
import { SLALL_LogBookFields } from './default-config';

/*

This file contains the default configuration for the vessel logbook
It is used to create the initial logbook for a vessel

Following is the description of the configuration fields:

    value       =>  The key in the logbook object, this value should be unique.
    label       =>  The label to display in the logbook.
    title       =>  Optional - the title to display in the logbook.
    status      =>  The status of the field (Required, Optional, Off).
    classes     =>  The classes to apply to the field (currently used to hide fields).
    groupTo     =>  The key of the field to group this field with.
    fieldSet    =>  The fieldset to group this field with.
    fieldType   =>  The type of field (files, text, etc).
    subCategory =>  Whether this field is a subcategory.
    forcedTitle =>  Whether to force the title to be displayed (this will not use the title from the DB).
    level       =>  The level of the field (1, 2, 3).
    tab         =>  The tab to display in the logbook.
    noCheck     =>  Whether to skip the check for this field. (Currently only available for Groups).

*/

const RequiredFields = [
  'OtherActivities',
  'CrewMemberID',
  'DutyPerformedID',
  'EngineID',
  'HoursStart',
  'HoursEnd',
  'Date',
  'LetGoTieUp',
  'FirstName',
  'Surname',
  'DailyChecksCompleted',
  'DepartTime',
  'From',
  'ArriveTime',
  'To',
  'ExpectedNextContact',
  'Hull',
  'OilAndWater',
  'SafetyEquipment',
  'TaskingStartUnderway',
  'TaskingOnScene',
  'TaskingOnTow',
  'TaskingPaused',
  'TaskingResumed',
  'TaskingComplete',
  'Supernumerary_Guests',
  'ConductSAP',
  'InvestigateNatureOfIssue',
  'EveryoneOnBoardOk',
  'RudderToMidshipsAndTrimmed',
  'LifejacketsOn',
  'CommunicationsEstablished',
  'SecureAndSafeTowing',
  'EPIRB',
  'FireExtinguisher',
  'EngineMountsAndStabilisers',
  'ElectricalVisualFields',
  'ElectricalChecks',
  'SteeringChecks',
  'EngineOilWater',
  'PreEngineAndPropulsion',
  'PostElectricalStrainers',
  'PostElectrical',
  'Steering',
  'PostEngineAndPropulsion',
  'DriveShaftsChecks',
  'OtherEngineFields',
  'MainEngineChecks',
  'EngineRoomVisualInspection',
  'FuelSystems',
  'PropulsionCheck',
  'Radio',
  'NavigationChecks',
  'OtherNavigation',
  'NavigationCharts',
  'Hull_DayShapes',
  'Hull_DeckEquipment',
  'Hull_HullStructure',
  'NavigationAndBridgeEquipment',
  'EngineRoomAndMachinery',
  'ElectricalSystems',
  'DeckOperations',
  'AccommodationAndGalley',
  'FinalChecks',
  'EventSupernumerary',
  'CrewTraining',
  'PassengerDeparture',
  'PassengerArrival',
  'BarCrossing',
  'RestrictedVisibility',
  'HumanRescue',
  'VehicleFerryFields',
  'SafetyCrewChecker',
  'PreCrewChecker',
  'PostCrewChecker',
  'OtherEngineCrewChecker',
  'NavigationCrewChecker',
  'JetCrewChecker',
  'CleaningCrewChecker',
  'DeckOpsCrewChecker',
  'HVACCrewChecker',
  'PlumbingCrewChecker',
  'SailCrewChecker',
  'BiosecCrewChecker',
  'EngrMechanical',
  'EngrGenerator',
  'EngrElectronics',
  'EngrTowlineWinch',
  'EngrCrewChecker',
  'PassengerVehiclePickDrop',
  'TripUpdate',
]


const getFilteredSlallLogBookFields = (
  vessel: any
): LogBookConfiguration[] => {
  const vesselTypeIndex = vesselTypes.indexOf(vessel?.vesselType);
  const carriesDangerousGoods = vessel?.vesselSpecifics?.carriesDangerousGoods;

  return SLALL_LogBookFields
    .filter(
      (field) =>
        Array.isArray(field.items) &&
        field.items.length > 0 &&
        field.vesselType.includes(vesselTypeIndex)
    )
    .map((field) => {
      const items = field.items.filter((item) => {
        const isVesselTypeMatch = item.vesselType.includes(vesselTypeIndex);
        const isNotDangerousGoods =
          carriesDangerousGoods === false
            ? item.classes !== 'dangerous-goods-sailing'
            : true;

        return isVesselTypeMatch && isNotDangerousGoods;
      });

      return { ...field, items };
    });
};

//Last sync at 10 Apr 2025

export { SLALL_LogBookFields, RequiredFields, getFilteredSlallLogBookFields };

export type * from './types';
