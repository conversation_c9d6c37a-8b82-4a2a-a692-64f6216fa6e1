import { type LogBookConfiguration } from "./types";

export const SLALL_LogBookFields: LogBookConfiguration[] = [
  // Crew Members - CrewMembers_LogBookComponent.
  {
    label: 'Crew Members',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Crew',
    // title: 'Crew',
    sortOrder: 1,
    componentClass: 'CrewMembers_LogBookComponent',
    items: [
      {
        value: 'CrewMemberID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew member',
        status: 'Required',
      },
      {
        value: 'DutyPerformedID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Duty performed',
        status: 'Required',
      },
      {
        value: 'PunchIn',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sign in',
        status: 'Required',
      },
      {
        value: 'PunchOut',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sign out',
        status: 'Required',
      },
      // {
      //     value: 'DutyHours',
      //     vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      //     label: 'Helm Time',
      //     status: 'Required',
      // },
      {
        value: 'WorkDetails',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Work details',
        status: 'Required',
      },
    ],
  },
  // Crew Training - CrewTraining_LogBookComponent.
  /* {
      label: 'Crew Training',
      vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      category: 'Crew',
      componentClass: 'CrewTraining_LogBookComponent',
      items: [
          {
              value: 'TrainerID',
              label: 'Trainer',
              vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
              status: 'Required',
          },
          {
              value: 'TrainingTypes',
              label: 'Nature of training',
              vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
              status: 'Required',
          },
          {
              value: 'TrainingLocation',
              label: 'Training location',
              vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
              status: 'Required',
          },
          {
              value: 'TrainingSummary',
              label: 'Training summary',
              vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
              status: 'Required',
          },
          {
              value: 'CrewMemberSignatures',
              label: 'Crew members',
              vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
              status: 'Required',
          },
      ],
  }, */
  // Engine Log - Engine_LogBookComponent.
  {
    label: 'Engine Log',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Engine Room',
    componentClass: 'Engine_LogBookComponent',
    items: [
      {
        value: 'HoursStart',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hours start',
        status: 'Required',
      },
      {
        value: 'HoursEnd',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hours end',
        status: 'Required',
      },
      {
        value: 'HoursRun',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hours run',
        status: 'Required',
      },
      {
        value: 'TotalHours',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Total hours',
        status: 'Required',
      },
      {
        value: 'FuelTankStartStops',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel records',
        status: 'Required',
      },
      {
        value: 'NauticalMiles',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Nautical miles',
        status: 'Required',
      },
      {
        value: 'FuelStart',
        label: 'Fuel start',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      // {
      //     value: 'FuelEnd',
      //     label: 'Fuel end',
      //     vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      //     status: 'Required',
      // },
      {
        value: 'FuelAdded',
        label: 'Fuel added',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelUsed',
        label: 'Fuel used',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelTemp',
        label: 'Fuel tempperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelPressure',
        label: 'Fuel pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelDiffPressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel diff pressure',
        status: 'Required',
      },
      {
        value: 'FuelRate',
        label: 'Fuel rate',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelDayTankLevel',
        label: 'Fuel day tank level',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'HeaderTankLevel',
        label: 'Header tank level',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'EngineFluidRecords',
        label: 'Engine fluid records',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'SewageDisposalRecords',
        label: 'Sewage disposal records',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'RPM',
        label: 'RPM',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'Boost',
        label: 'Boost',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ManifoldTemp',
        label: 'Manifold temp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'GenSetTemp',
        label: 'Generator tempetature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'CoolantTemp',
        label: 'Coolant temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'CoolantLevelOK',
        label: 'Coolant level OK',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ThrustBearingTemp',
        label: 'Thrust bearing temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ShaftBearingTemp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Shaft bearing temperature',
        status: 'Required',
      },
      {
        value: 'OilLevelOK',
        label: 'Oil level OK',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'OilPressure',
        label: 'Oil pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'LubeOilLevelOK',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lube oil level OK',
        status: 'Required',
      },
      {
        value: 'LubeOilTemp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lube oil temperature',
        status: 'Required',
      },
      {
        value: 'LubeOilPressure',
        label: 'Lube oil pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'LubeFilterPressure',
        label: 'Lube filter pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'Volts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Volts',
        status: 'Required',
      },
      {
        value: 'KWLoad',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Kilowatt load',
        status: 'Required',
      },
      {
        value: 'OverboardPressure',
        label: 'Overboard pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'OverboardDischarge',
        label: 'Overboard discharge',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'Comments',
        label: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'Signature',
        label: 'Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
    ],
  },
  // Engineering Details - Engineer_LogBookComponent.
  {
    label: 'Engineering Details',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Engine Room',
    componentClass: 'Engineer_LogBookComponent',
    items: [
      {
        value: 'Date',
        label: 'Date',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'EngineerComments',
        label: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'EngineerWorkOrders',
        label: 'Work orders',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'VehicleDutySessions',
        label: 'Vessel duty session',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'SMURecords',
        label: 'SMU meter readings',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ManifoldTemp',
        label: 'Manifold temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'GenSetTemp',
        label: 'Generator temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'CoolantTemp',
        label: 'Coolant temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'FuelTemp',
        label: 'Fuel temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ThrustBearingTemp',
        label: 'Thrust bearing temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'ShaftBearingTemp',
        label: 'Shaft bearing temperature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'OilPressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Oil pressure',
        status: 'Required',
      },
      {
        value: 'FuelPressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel pressure',
        status: 'Required',
      },
      {
        value: 'FuelRate',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel rate',
        status: 'Required',
      },
      {
        value: 'Volts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Volts',
        status: 'Required',
      },
      {
        value: 'KWLoad',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Kilowatt load',
        status: 'Required',
      },
      {
        value: 'OverboardPressure',
        label: 'Overboard pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'OverboardDischarge',
        label: 'Overboard discharge',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'Pyros',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pyros',
        status: 'Required',
      },
      {
        value: 'Boost',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Boost',
        status: 'Required',
      },
      {
        value: 'WaterTemp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Water temperature',
        status: 'Required',
      },
      {
        value: 'AirTemp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Air temperature',
        status: 'Required',
      },
      {
        value: 'RPM',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'RPM',
        status: 'Required',
      },
      {
        value: 'Rack',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Rack',
        status: 'Required',
      },
      {
        value: 'GenSetOP',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Genset O/P',
        status: 'Required',
      },
      {
        value: 'GenSetWT',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Genset W/T',
        status: 'Required',
      },
      {
        value: 'GearboxOP',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Gearbox O/P',
        status: 'Required',
      },
      {
        value: 'GearboxCLOP',
        label: 'Gearbox C/L/O',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        status: 'Required',
      },
      {
        value: 'GearboxOT',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Gearbox O/T',
        status: 'Required',
      },
      {
        value: 'HRPOP',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'HRP O/P',
        status: 'Required',
      },
      {
        value: 'SeaLogsMemberID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engineer',
        status: 'Required',
      },
    ],
  },
  // Fuel - Fuel_LogBookComponent.
  {
    label: 'Fuel',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Engine Room',
    componentClass: 'Fuel_LogBookComponent',
    items: [
      {
        value: 'HazardousSubstanceRecords',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hazardous substances',
        status: 'Required',
      },
      {
        value: 'FuelTankStartStops',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel records',
        status: 'Required',
      },
      {
        value: 'EngineFluidRecords',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine fluid records',
        status: 'Required',
      },
      {
        value: 'SewageDisposalRecords',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sewage disposal records',
        status: 'Required',
      },
      {
        value: 'FuelTankID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel tank',
        status: 'Required',
      },
      {
        value: 'Start',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Start level',
        status: 'Required',
      },
      {
        value: 'End',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'End level',
        status: 'Required',
      },
      {
        value: 'Added',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel added',
        status: 'Required',
      },
    ],
  },
  // Shipping Log's - Ports_LogBookComponent.
  {
    label: 'Shipping Logs',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    componentClass: 'Ports_LogBookComponent',
    items: [
      {
        value: 'LetGoTieUp',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Let Go - Tie Up',
        status: 'Required',
      },
      {
        value: 'ChecksCompleted',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Checks completed in accordance to vessel',
        status: 'Required',
      },
      {
        value: 'PilotTransfers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pilot transfers',
        status: 'Required',
      },
      {
        value: 'ShipTugs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tug logs',
        status: 'Required',
      },
      {
        value: 'OtherActivities',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Other activity',
        status: 'Required',
      },
      {
        value: 'PPBTransfers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'PPB transfers',
        status: 'Required',
      },
      {
        value: 'MasterID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Master',
        status: 'Required',
      },
      {
        value: 'TripCrewList',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew list',
        status: 'Required',
      },
      {
        value: 'SafetyKayakers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engineers',
        status: 'Required',
      },
      {
        value: 'FuelUsed',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel used',
        status: 'Required',
      },
      {
        value: 'Supernumerary',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Supernumerary',
        status: 'Required',
      },
      {
        value: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Comments',
        status: 'Required',
      },
      {
        value: 'Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Signature initial',
        status: 'Required',
      },
    ],
  },
  // Supernumerary - Supernumerary_LogBookComponent.
  {
    label: 'Supernumerary',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Crew',
    sortOrder: 2,
    componentClass: 'Supernumerary_LogBookComponent',
    items: [
      {
        value: 'FirstName',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'First name',
        status: 'Required',
      },
      {
        value: 'Surname',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Surname',
        status: 'Required',
      },
      {
        value: 'Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Signature',
        status: 'Required',
      },
      {
        value: 'Policies',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Policies',
        status: 'Required',
        fieldType: 'files',
      },
    ],
  },
  // Trip Log - TripReport_LogBookComponent.
  {
    label: 'Trip Log',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    componentClass: 'TripReport_LogBookComponent',
    items: [
      {
        value: 'VehicleFerryFields',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Passenger/vehicle ferries and water taxis',
        // title: 'Passenger / Vehicle ferries and water taxis',
        status: 'Required',
        noCheck: true,
      },
      {
        value: 'DepartFrom',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depart from',
        status: 'Required',
      },
      {
        value: 'DepartTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depart time',
        status: 'Required',
      },
      {
        value: 'Depart',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depart',
        status: 'Required',
      },
      {
        value: 'From',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depart location',
        status: 'Required',
      },
      {
        value: 'ExpectedNextContact',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Next expected contact time',
        status: 'Required',
      },
      {
        value: 'DepartArrive',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depart - arrive',
        status: 'Required',
      },
      {
        value: 'PositionLogs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Position logs',
        status: 'Required',
      },
      // {
      //     value: 'DangerousGoods',
      //     vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      //     label: 'Dangerous goods',
      //     status: 'Required',
      //     groupTo: 'VehicleFerryFields',
      // },
      // {
      //     value: 'DesignatedDangerousGoodsSailing',
      //     vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      //     label: 'Separate designated dangerous goods sailing',
      //     status: 'Required',
      //     groupTo: 'VehicleFerryFields',
      // },
      {
        value: 'DailyChecksCompleted',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Checks completed in accordance to vessel',
        status: 'Required',
      },
      {
        value: 'PaxJoinedBreakDown',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax joined',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedAdult',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Adult',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedChild',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Child',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedYouth',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Youth',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedVoucher',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Voucher',
        status: 'Required',
      },
      {
        value: 'PaxJoinedFOC',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'F.O.C.',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedStaff',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Staff',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'PaxJoinedPrePaid',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Prepaid',
        status: 'Required',
      },
      {
        value: 'PaxDeparted',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax departed',
        status: 'Required',
      },
      {
        value: 'NumberPax',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Total pax carried',
        status: 'Required',
      },
      {
        value: 'VoucherNumber',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Voucher number',
        status: 'Required',
      },
      {
        value: 'SafetyBriefing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Safety briefing',
        status: 'Required',
      },
      {
        value: 'SpeedExemption',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Speed exemption used',
        status: 'Required',
      },
      {
        value: 'SpeedExemptionCorridorID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Corridor',
        status: 'Required',
      },
      {
        value: 'SpeedExemptionReasonID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Reason',
        status: 'Required',
      },
      {
        value: 'VehiclesJoined',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Vehicles joined',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'VehiclesDeparted',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Vehicles departed',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'TotalVehiclesCarried',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Total vehicles carried',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'VOB',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Vehicles onboard',
        status: 'Required',
        groupTo: 'VehicleFerryFields',
      },
      {
        value: 'TripReport_Stops',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Trip stops',
        status: 'Required',
      },
      {
        value: 'VehicleDrivers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Vehicle drivers',
        status: 'Required',
      },
      {
        value: 'TripEvents',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Trip events',
        status: 'Required',
      },
      {
        value: 'IncidentReports',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Incident report',
        status: 'Required',
      },
      {
        value: 'HazardReports',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hazard report',
        status: 'Required',
      },
      {
        value: 'MasterID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Master',
        status: 'Required',
      },
      {
        value: 'LeadGuideID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lead guide',
        status: 'Required',
      },
      {
        value: 'TripCrewList',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew list',
        status: 'Required',
      },
      {
        value: 'SafetyKayakers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Safety kayakers',
        status: 'Required',
      },
      {
        value: 'ArriveTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Arrival time',
        status: 'Required',
      },
      {
        value: 'Arrive',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Arrive',
        status: 'Required',
      },
      {
        value: 'To',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Destination',
        status: 'Required',
      },
      {
        value: 'ArriveTo',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Arrived to',
        status: 'Required',
      },
      {
        value: 'ToLat',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Latitude',
        status: 'Required',
      },
      {
        value: 'ToLong',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Longitude',
        status: 'Required',
      },
      {
        value: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Comments',
        status: 'Required',
      },
      {
        value: 'Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Signature initial',
        status: 'Required',
      },
      {
        value: 'POB',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Passengers onboard',
        status: 'Required',
      },
      {
        value: 'RadioLog',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Radio log',
        status: 'Required',
        hasDynamicChildren: true,
      },
    ],
  },
  // Daily Checks - VesselDailyCheck_LogBookComponent.
  {
    label: 'Pre-Departure Checks',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    sortOrder: 3,
    componentClass: 'VesselDailyCheck_LogBookComponent',
    subCategory: true,
    items: [
      {
        value: 'SafetyCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Safety Checks',
      },
      {
        value: 'CheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Check time',
        status: 'Required',
        // fieldSet: 'Safety Checks',
        groupTo: 'SafetyCrewChecker',
      },
      {
        value: 'BilgeLevels',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bilge levels',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'BilgePumps',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bilge pumps',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'Hull',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hull and superstructure',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'NavEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation equipment',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'OilAndWater',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Machinery oil and water',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'MainEngineChecks',
      },
      {
        value: 'EngineRoomChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine room checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineRoomVisualInspection',
      },
      {
        value: 'SafetyEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Safety equipment',
        status: 'Required',
        fieldSet: 'Safety Checks',
        // title: 'Safety Equipment',
      },
      {
        value: 'DriveShafts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Drive shafts',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'DriveShaftsChecks',
      },
      {
        value: 'DriveShaftsChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Drive shaft checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        tab: 'Other engine checks',
      },
      {
        value: 'GearBox',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Gear box',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'DriveShaftsChecks',
      },
      {
        value: 'Propeller',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Propeller',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'DriveShaftsChecks',
      },
      {
        value: 'Skeg',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Skeg',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'DriveShaftsChecks',
      },
      {
        value: 'ChecksWithManual',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Checks in accordance with the manual',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'BeltsHosesClamps',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Belts, hoses, clamps and fittings',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'Cabin',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cabin',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'Floor',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Floor',
        status: 'Required',
        fieldSet: 'Checks',
      },
      {
        value: 'EngineMountsAndStabilisers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine mounts and stabilisers',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'EngineMounts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine mounts',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineMountsAndStabilisers',
      },
      {
        value: 'Stabilizers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Stabilizers',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Inspect Stabilizers',
        groupTo: 'EngineMountsAndStabilisers',
      },
      {
        value: 'EngineTellTale',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine telltale signs',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'MainEngineChecks',
      },
      {
        value: 'EngineIsFit',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine controls tested',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'MainEngineChecks',
      },
      {
        value: 'SteeringFluid',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering fluid',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringRams',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering rams',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringIsFit',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering checked',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'OtherEngineFields',
        // groupTo: 'SteeringChecks',
      },
      {
        value: 'OperationalTestsOfHelm',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Operational tests of helm',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'OtherEngineFields',
        // groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringHydraulicSystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering hydraulic systems',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringTillers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering tiller',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'OtherEngineFields',
        // groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringHoses',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering hoses',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'SteeringRudders',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering rudders',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'EPIRB',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'EPIRB',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'FirstAid',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'First aid',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'LifeJackets',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Life jackets',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'FireExtinguisher',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire essentials',
        status: 'Required',
        fieldSet: 'Safety Checks',
        // title: 'Fire essentials',
      },
      {
        value: 'PersonOverboardRescueEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Person overboard rescue equipment',
        status: 'Required',
        fieldSet: 'Safety Checks',
        // title: 'Person Overboard Rescue Equipment',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'SmokeDetectors',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Smoke detectors',
        status: 'Required',
        fieldSet: 'Safety Checks',
        // title: 'Smoke and carbon monoxide detectors test',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'ForwardAndReverseBelts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Forward & reverse belts',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'SteeringTiller',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering, tiller, arm bolts',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'CablesFRPullies',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cables & pullies',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'ThrottleAndCable',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Throttle & cable',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'SandTraps',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sand traps',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'SteeringChecks',
      },
      {
        value: 'UnitTransomBolts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Unit transom bolts',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'CotterPins',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cotter pins',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'ReverseBucketAndRam',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Reverse bucket & rams',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'NozzleAndBearings',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Nozzle & bearings',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'TailHousing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tail housing & bolts',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'WeatherSummary',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Weather forecast summary',
        status: 'Required',
      },
      {
        value: 'WindDirection',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wind direction',
        status: 'Required',
      },
      {
        value: 'WindStrength',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wind strength',
        status: 'Required',
      },
      {
        value: 'Swell',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sea swell',
        status: 'Required',
      },
      {
        value: 'TimeHighTide',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'High tide time',
        status: 'Required',
      },
      {
        value: 'LevelHighTide',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'High tide level',
        status: 'Required',
      },
      {
        value: 'TimeLowTide',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Low tide time',
        status: 'Required',
      },
      {
        value: 'LevelLowTide',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Low tide level',
        status: 'Required',
      },
      {
        value: 'Tides',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tide predictions',
        status: 'Required',
      },
      {
        value: 'MasterID',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew member',
        status: 'Required',
      },
      {
        value: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Maintenance/corrective actions',
        status: 'Required',
      },
      {
        value: 'Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Signature',
        status: 'Required',
      },
      {
        value: 'LifeRings',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Life rings',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'Flares',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Flares',
        status: 'Required',
        fieldSet: 'Safety Checks',
        // title: 'Flares, visual distress signals',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'FireHoses',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire hoses',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'FireBuckets',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire buckets',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'FireBlanket',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire blanket',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'FireAxes',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire axes',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'FirePump',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire pump',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'FireFlaps',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire flaps',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'FireExtinguisher',
      },
      {
        value: 'LifeRaft',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Life raft',
        status: 'Required',
        fieldSet: 'Safety Checks',
        groupTo: 'SafetyEquipment',
      },
      {
        value: 'HighWaterAlarm',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'High water alarm',
        status: 'Required',
        fieldSet: 'Safety Checks',
      },
      {
        value: 'CrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew responsible',
        status: 'Required',
        // fieldSet: 'Safety Checks',
        groupTo: 'SafetyCrewChecker',
      },
      {
        value: 'Exterior',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Exterior',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'Interior',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Interior',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'Charts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Charts',
        // title: 'Appropriate charts for sailing area',
        status: 'Required',
        groupTo: 'NavigationCharts',
      },
      {
        value: 'NavigationCharts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation charts',
        // title: 'Charts',
        fieldSet: 'Navigation',
        status: 'Required',
      },
      {
        value: 'OtherNavigation',
        fieldSet: 'Navigation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Other',
        // title: 'Other',
        status: 'Required',
      },
      {
        value: 'DocumentCrewBriefings',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Document crew briefings',
        status: 'Required',
        fieldSet: 'Documentation',
      },
      {
        value: 'RecordComments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Record comments',
        status: 'Required',
        fieldSet: 'Documentation',
      },
      {
        value: 'PropulsionEngineChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'MainEngineChecks',
      },
      {
        value: 'Batteries',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Batteries',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'Bilge',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bilge',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'Throttle',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Throttle',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'JetUnit',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Jet unit',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'PreStartupChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pre-startup checks',
        status: 'Required',
        fieldSet: 'Engine Checks',
        // title: 'Pre-startup checks',
        level: 3,
      },
      {
        value: 'PreEngineAndPropulsion',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine and propulsion',
        // title: 'Engine and Propulsion',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'PostEngineAndPropulsion',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine and propulsion',
        // title: 'Engine and Propulsion',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Post-startup checks',
      },
      {
        value: 'PostElectrical',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical',
        // title: 'Electrical',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Post-startup checks',
      },
      {
        value: 'OtherEngineFields',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Other engine fields',
        // title: 'Other Engine Fields',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Other engine checks',
      },
      {
        value: 'PreFuelLevelStart',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level start',
        // title: 'Fuel Level Start',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreEngineAndPropulsion',
      },
      // {
      //     value: 'PreFuelLevelEnd',
      //     vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      //     label: 'Fuel level end',
      //     // title: 'Fuel Level End',
      //     status: 'Required',
      //     fieldSet: 'Engine Checks',
      //     groupTo: 'PreEngineAndPropulsion',
      // },
      {
        value: 'EngineHoursStart',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine hours start',
        // title: 'Engine Hours Start',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'EngineHoursEnd',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine hours end',
        // title: 'Engine Hours End',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'CheckOilPressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Check oil pressure',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostEngineAndPropulsion',
      },
      {
        value: 'BatteryIsCharging',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Battery is charging',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostElectrical',
      },
      {
        value: 'ShorePowerIsDisconnected',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Shore power is disconnected',
        // title: 'Shore power is disconnected',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostElectrical',
      },
      {
        value: 'LockToLockSteering',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lock-to-lock steering',
        // title: 'Lock-to-lock steering',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'Steering',
      },
      {
        value: 'SteeringTrimTabs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Trim tabs',
        // title: 'Trim tabs',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'Steering',
      },
      {
        value: 'EngineChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine checks',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreStartupChecks',
      },
      {
        value: 'EngineCheckPropellers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Propellers',
        // title: 'Propellers',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'EngineOilWater',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine oil & water',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'EngineOil',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine oil',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineOilWater',
      },
      {
        value: 'OilWater',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Oil & water',
        // title: 'Oil & Water',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngineOilWater',
      },
      {
        value: 'Electrical',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreStartupChecks',
      },
      {
        value: 'PostStartupChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Post-startup checks',
        status: 'Required',
        fieldSet: 'Engine Checks',
        // title: 'Post-startup checks',
        level: 3,
      },
      {
        value: 'OtherEngineChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Other engine checks',
        status: 'Required',
        fieldSet: 'Engine Checks',
        // title: 'Other Engine Checks',
        level: 3,
      },
      {
        value: 'PostStartupEngineChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Post-startup engine checks',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostStartupChecks',
      },
      {
        value: 'Propulsion',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Propulsion',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostStartupChecks',
      },
      {
        value: 'ForwardAndReverse',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Forward and reverse',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostStartupChecks',
      },
      {
        value: 'Cooling',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cooling system checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Cooling system checks',
        groupTo: 'PostElectricalStrainers',
      },
      {
        value: 'CoolantLevels',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Coolant levels',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PostEngineAndPropulsion',
        // title: 'Temprature is in range',
      },
      {
        value: 'PropulsionPropulsion',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Propulsion',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'EngineRoom',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine room',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineRoomVisualInspection',
      },
      {
        value: 'AirShutoffs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Air shut-offs',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineRoomVisualInspection',
      },
      {
        value: 'FireDampeners',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fire dampeners',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'EngineRoomVisualInspection',
      },
      {
        value: 'Generator',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Generator',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'ElectricalChecks',
      },
      {
        value: 'FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'FuelSystems',
      },
      {
        value: 'FuelShutoffs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel shut-offs',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'Separators',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Separators',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'Hull_HullStructure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hull structure',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Hull and superstructure inspections',
      },
      {
        value: 'HullStructure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hull structure',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Hull and superstructure inspections',
        groupTo: 'Hull_HullStructure',
      },
      {
        value: 'PontoonPressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pontoon pressure (for inflatables)',
        // title: 'Pontoon pressure (for inflatables)',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_HullStructure',
      },
      {
        value: 'BungsInPlace',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bungs in place',
        // title: 'Bungs in place',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_HullStructure',
      },
      {
        value: 'Hatches',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hatches',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Hatches',
        groupTo: 'Hull_HullStructure',
      },
      {
        value: 'Hull_DeckEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Deck equipment',
        // title: 'Deck Equipment',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
      },
      {
        value: 'DeckEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Deck equipment',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Deck equipment',
        groupTo: 'Hull_DeckEquipment',
      },
      {
        value: 'SwimPlatformLadder',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Swim platform, ladder',
        // title: 'Swim platform, ladder',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_DeckEquipment',
      },
      {
        value: 'BiminiTopsCanvasCovers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bimini tops, canvas covers',
        // title: 'Bimini tops, canvas covers',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_DeckEquipment',
      },
      {
        value: 'Hull_DayShapes',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Day shapes',
        // title: 'Day Shapes',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
      },
      {
        value: 'DayShapes',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Day shapes',
        // title: 'Day Shapes',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_DayShapes',
      },
      {
        value: 'HullNavigationLights',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation lights',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        groupTo: 'Hull_DayShapes',
      },
      {
        value: 'TenderOperationalChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tender operational checks',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
      },
      {
        value: 'Anchor',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Anchor',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Anchor, warps, chains, windlass/hauling equipment',
      },
      {
        value: 'WindscreenCheck',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Windscreen check',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Check windscreen for clarity of sight',
      },
      {
        value: 'NightLineDockLinesRelease',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Night line/dock lines release',
        status: 'Required',
        fieldSet: 'Deck operations and exterior checks',
        // title: 'Night line / dock lines are ready for release',
      },
      {
        value: 'HVAC',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'HVAC',
        status: 'Required',
        fieldSet: 'HVAC',
      },
      {
        value: 'TV',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'TV',
        status: 'Required',
        fieldSet: 'HVAC',
      },
      {
        value: 'StabilizationSystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Stabilization systems',
        status: 'Required',
        fieldSet: 'HVAC',
      },
      {
        value: 'Electronics',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electronics',
        status: 'Required',
        fieldSet: 'HVAC',
      },
      {
        value: 'NavigationChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation checks',
        status: 'Required',
        fieldSet: 'Navigation',
        // title: 'Navigation Checks',
      },
      {
        value: 'GPS',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'GPS',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'DepthSounder',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Depth sounder',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'Radar',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Radar',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'TracPlus',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'TracPlus',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'ChartPlotter',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Chart plotter',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'SART',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'SART',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'AISOperational',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'AIS operational',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'NavigationChecks',
      },
      {
        value: 'Radio',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Radio',
        status: 'Required',
        fieldSet: 'Navigation',
      },
      {
        value: 'VHF',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'VHF',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'Radio',
      },
      {
        value: 'UHF',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'UHF',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'Radio',
      },
      {
        value: 'NavigationLights',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation lights',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'OtherNavigation',
      },
      {
        value: 'Compass',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Compass',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'OtherNavigation',
      },
      {
        value: 'SoundSignallingDevices',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sound signalling devices',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'OtherNavigation',
      },
      {
        value: 'NavigationHazards',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation hazards',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'OtherNavigation',
      },
      {
        value: 'Wheelhouse',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wheelhouse',
        status: 'Required',
        fieldSet: 'Navigation',
        groupTo: 'OtherNavigation',
      },
      {
        value: 'BilgeCheck',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bilge check',
        status: 'Required',
        fieldSet: 'Plumbing',
      },
      {
        value: 'Sewage',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sewage',
        status: 'Required',
        fieldSet: 'Plumbing',
      },
      {
        value: 'FreshWater',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fresh water',
        status: 'Required',
        fieldSet: 'Plumbing',
      },
      {
        value: 'Sanitation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sanitation',
        status: 'Required',
        fieldSet: 'Plumbing',
      },
      {
        value: 'PestControl',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pest control',
        status: 'Required',
        fieldSet: 'Plumbing',
      },
      {
        value: 'MainEngineChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Main engine(s) checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Main Engine checks',
        tab: 'Other engine checks',
      },
      {
        value: 'EngineRoomVisualInspection',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine room visual inspection',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Engine room visual inspection',
        tab: 'Other engine checks',
      },
      {
        value: 'FuelSystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel systems',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        tab: 'Other engine checks',
      },
      {
        value: 'SteeringChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering checks',
        // forcedTitle: 'Steering',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Steering Checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'ThrottleAndCableChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Throttle and cable checks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'OtherEngineFields',
      },
      {
        value: 'ElectricalChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical checks',
        // forcedTitle: 'Electrical',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Electrical checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'Steering',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        tab: 'Post-startup checks',
      },
      {
        value: 'FuelTanks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel tanks',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PostElectricalStrainers',
      },
      {
        value: 'FuelFilters',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel filters',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PreEngineAndPropulsion',
      },
      {
        value: 'Fuel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel',
        status: 'Required',
        fieldSet: 'Fuel Checks',
      },
      {
        value: 'MainEngine',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Main engine',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'Transmission',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Transmission',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'SteeringPropulsion',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Steering propulsion',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'PropulsionCheck',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Propulsion check',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        tab: 'Other engine checks',
      },
      {
        value: 'ForwardReverse',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Forward & reverse',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PostEngineAndPropulsion',
      },
      {
        value: 'TrimTabs',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Trim tabs',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'AzimuthThruster',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Azimuth thrusters',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PropulsionCheck',
      },
      {
        value: 'Exhaust',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Exhaust',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Exhaust system',
        groupTo: 'PostElectricalStrainers',
      },
      {
        value: 'PropulsionBatteriesStatus',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Batteries status',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'ElectricalChecks',
        // title: 'Batteries Status',
      },
      {
        value: 'HouseBatteriesStatus',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'House batteries status',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'ElectricalChecks',
        // title: 'House Batteries Status',
      },
      {
        value: 'ShorePower',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Shore power',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Shore Power',
        groupTo: 'ElectricalChecks',
      },
      {
        value: 'ElectricalVisualFields',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical panels',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Electrical Panels',
        tab: 'Pre-startup checks',
      },
      {
        value: 'ElectricalPanels',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical panels',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'ElectricalVisualFields',
        // title: 'Electrical Panels',
      },
      {
        value: 'Wiring',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wiring',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'ElectricalVisualFields',
      },
      {
        value: 'PostElectricalStrainers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sea strainers',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        // title: 'Sea Strainers',
        tab: 'Post-startup checks',
      },
      {
        value: 'SeaStrainers',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sea strainers',
        status: 'Required',
        fieldSet: 'Engine Checks', // Previously Propusion
        groupTo: 'PostElectricalStrainers',
        // title: 'Sea Strainers',
      },
      {
        value: 'Sail',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sail',
        status: 'Required',
        fieldSet: 'Sail',
        // title: 'Sail',
      },
      {
        value: 'PreCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Pre-startup checks',
      },
      {
        value: 'PreCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreCrewChecker',
      },
      {
        value: 'PreCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PreCrewChecker',
      },
      {
        value: 'PostCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Post-startup checks',
      },
      {
        value: 'PostCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostCrewChecker',
      },
      {
        value: 'PostCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'PostCrewChecker',
      },
      {
        value: 'OtherEngineCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Other engine checks',
      },
      {
        value: 'OtherEngineCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineCrewChecker',
      },
      {
        value: 'OtherEngineCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'OtherEngineCrewChecker',
      },
      {
        value: 'JetCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Jet Specific Checks',
      },
      {
        value: 'JetCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'JetCrewChecker',
      },
      {
        value: 'JetCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'JetCrewChecker',
      },
      {
        value: 'CleaningCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleaningCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'CleaningCrewChecker',
      },
      {
        value: 'CleaningCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'CleaningCrewChecker',
      },
      {
        value: 'NavigationCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'Navigation',
        status: 'Required',
      },
      {
        value: 'NavigationCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'NavigationCrewChecker',
      },
      {
        value: 'NavigationCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'NavigationCrewChecker',
      },
      {
        value: 'DeckOpsCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'Deck operations and exterior checks',
        status: 'Required',
      },
      {
        value: 'DeckOpsCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'DeckOpsCrewChecker',
      },
      {
        value: 'DeckOpsCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'DeckOpsCrewChecker',
      },
      {
        value: 'HVACCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'HVAC',
        status: 'Required',
      },
      {
        value: 'HVACCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'HVACCrewChecker',
      },
      {
        value: 'HVACCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'HVACCrewChecker',
      },
      {
        value: 'PlumbingCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'Plumbing',
        status: 'Required',
      },
      {
        value: 'PlumbingCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'PlumbingCrewChecker',
      },
      {
        value: 'PlumbingCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'PlumbingCrewChecker',
      },
      {
        value: 'SailCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'Sail',
        status: 'Required',
      },
      {
        value: 'SailCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'SailCrewChecker',
      },
      {
        value: 'SailCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'SailCrewChecker',
      },
      {
        value: 'EngineeringChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engineering',
        status: 'Required',
        fieldSet: 'Engine Checks',
        level: 3,
      },
      {
        value: 'EngrMechanical',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Mechanical',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Engineering',
      },
      {
        value: 'MechCrankcaseOilLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crankcase Oil Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechCoolingWaterLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cooling Water Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechTransmissionOilLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Transmission Oil Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechInspectPipework',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Inspect Pipework',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechHydraulicSteeringOilLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hydraulic Steering Oil Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechGearBoxOilLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Gear Box Oil Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'MechInspectVeeBelts',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Inspect Vee Belts',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrMechanical',
      },
      {
        value: 'EngrGenerator',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Generator',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Engineering',
      },
      {
        value: 'GenCrankcaseOilLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crankcase Oil Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'GenCoolingWaterLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cooling Water Level',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'GenElectrical',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'GenPracxisSystemOperative',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Check Pracxis System Operative',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'GenTest24VLighting',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Test 24V Lighting',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'GenRunningTankFuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Record Running Tank Fuel Level per Shipping Movement',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrGenerator',
      },
      {
        value: 'EngrElectronics',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electronics',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Engineering',
      },
      {
        value: 'ElectrDeckLights',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Deck Lights',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrElectronics',
      },
      {
        value: 'ElectrSearchLights',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Search Lights',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrElectronics',
      },
      {
        value: 'ElectrChart',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Chart',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrElectronics',
      },
      {
        value: 'EngrTowlineWinch',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Towline & Winch',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Engineering',
      },
      {
        value: 'TowCheckWinchCondition',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Check Winch condition',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrTowlineWinch',
      },
      {
        value: 'TowProveWinchOperation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Prove Winch Operation',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrTowlineWinch',
      },
      {
        value: 'TowSelectControlStation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Select Control Station',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrTowlineWinch',
      },
      {
        value: 'TowCheckTowlineCondition',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Check Towline Condition',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrTowlineWinch',
      },

      {
        value: 'EngrCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        tab: 'Engineering',
      },
      {
        value: 'EngrCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrCrewChecker',
      },
      {
        value: 'EngrCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        fieldSet: 'Engine Checks',
        groupTo: 'EngrCrewChecker',
      },

      {
        value: 'BiosecGlueBoardTraps',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Glue-board traps checked',
        status: 'Required',
        fieldSet: 'Biosecurity',
      },
      {
        value: 'BiosecCrewChecker',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        fieldSet: 'Biosecurity',
        status: 'Required',
      },
      {
        value: 'BiosecCrewResponsible',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew Responsible',
        status: 'Required',
        groupTo: 'BiosecCrewChecker',
      },
      {
        value: 'BiosecCheckTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time Checked',
        status: 'Required',
        groupTo: 'BiosecCrewChecker',
      },
      {
        value: 'CleanGalleyBench',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Galley Bench',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanGalleyFloor',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Galley Floor',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanTable',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tables',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanMirrorGlass',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Mirrors / Glass',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanToilet',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Toilets',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanSink',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'All Sinks',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanDeckFloor',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Deck Floors',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanOutsideWallWindow',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Outside Walls and Windows',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanGarbageBin',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Rubbish / Garbage Bins',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanBoothSeat',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Booths / Seats',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanFridge',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fridges',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanCupboard',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cupboards',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanOven',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Oven',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanSouvenir',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Souvenirs',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanRestockSalesItem',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Restock Sales Items',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
      {
        value: 'CleanTill',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Till',
        status: 'Required',
        fieldSet: 'Cleaning Checks',
      },
    ],
  },
  // Weather - Weather_LogBookComponent.
  {
    label: 'Weather',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    sortOrder: 9,
    componentClass: 'Weather_LogBookComponent',
    items: [
      {
        value: 'Tides',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tides',
        status: 'Required',
      },
    ],
  },
  // Voyage Summary - VoyageSummary_LogBookComponent.
  {
    label: 'Voyage Summary',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    // title: 'Voyage Summary',
    sortOrder: 9,
    componentClass: 'VoyageSummary_LogBookComponent',
    items: [
      {
        value: 'Activities',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Activities undertaken',
        status: 'Required',
      },
      {
        value: 'CourseSteered',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Course steered',
        status: 'Required',
      },
      {
        value: 'CourseOverGround',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Course over ground',
        status: 'Required',
      },
      {
        value: 'ChangesToPlan',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Changes to voyage',
        status: 'Required',
      },
      {
        value: 'SpeedOverGround',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Speed over ground',
        status: 'Required',
      },
      {
        value: 'VesselRPM',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Vessel RPM',
        status: 'Required',
      },
      {
        value: 'TypeOfSteering',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Type of steering',
        status: 'Required',
      },
      {
        value: 'VoyageDistance',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Voyage distance',
        status: 'Required',
      },
      {
        value: 'Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Unusual occurrences',
        status: 'Required',
      },
      {
        value: 'Oktas',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cloud cover',
        status: 'Required',
      },
      {
        value: 'WindStrength',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wind strength (kts)',
        status: 'Required',
      },
      {
        value: 'WindDirection',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Wind direction',
        status: 'Required',
      },
      {
        value: 'Precipitation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Precipitation',
        status: 'Required',
      },
      {
        value: 'Pressure',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Barometeric pressure',
        status: 'Required',
      },
      {
        value: 'Visibility',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Visibility',
        status: 'Required',
      },
      {
        value: 'Swell',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Swell',
        status: 'Required',
      },
      {
        value: 'SeaState',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sea state',
        status: 'Required',
      },
      {
        value: 'WeatherComments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Comments on conditions',
        status: 'Required',
      },
      {
        value: 'ForecastComment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Forecast comments',
        status: 'Required',
      },
      {
        value: 'WeatherReports',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Weather reports',
        status: 'Required',
      },
      {
        value: 'Tides',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Tide predictions',
        status: 'Required',
      },
    ],
  },
  // Crew Welfare - CrewWelfare_LogBookComponent.
  {
    label: 'Crew Welfare',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Crew',
    componentClass: 'CrewWelfare_LogBookComponent',
    items: [
      {
        value: 'Fitness',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Safe crewing assessment',
        status: 'Required',
        // title: 'Safe crewing assessment',
      },
      {
        value: 'SafetyActions',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Risk assessments completed',
        status: 'Required',
        // title: 'Risk assessments completed',
      },
      {
        value: 'WaterQuality',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Health, safety, and environment actions',
        status: 'Required',
        // title: 'Health, safety and environment actions',
      },
      {
        value: 'IMSafe',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'IMSafe',
        status: 'Required',
        // title: 'IMSAFE',
        description:
          'Free of illness and symptoms, using only safe medication, managing stress well at home and work, free of alcohol, drugs, and their effects, rested, well-slept, and free of fatigue, well-fed, hydrated, and ready to go.',
      },
    ],
  },
  // LogBook sign-off - LogBookSignOff_LogBookComponent.
  {
    label: 'Logbook Sign-Off',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'SignOff',
    componentClass: 'LogBookSignOff_LogBookComponent',
    items: [
      {
        value: 'Review',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Logbook entry review',
        status: 'Required',
        // title: 'Logbook entry review',
      },
      {
        value: 'SafetyEquipmentCheck',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Safety equipment check',
        status: 'Required',
        // title: 'Safety equipment check',
      },
      {
        value: 'ForecastAccuracy',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Forecast accuracy',
        status: 'Required',
        // title: 'Forecast Accuracy',
      },
      {
        value: 'FuelEnd',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel end',
        status: 'Required',
        // title: 'Fuel start',
      },
      {
        value: 'EngineHours',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine hours',
        status: 'Required',
        // title: 'Engine Hours',
      },
      {
        value: 'EngineHoursEnd',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hours run',
        status: 'Required',
        // title: 'Hours Run',
      },
      {
        value: 'AIS',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Automatic Identification System (AIS)',
        status: 'Required',
        // title: 'Automatic Identification System (AIS)',
        groupTo: 'NavigationAndBridgeEquipment',
      },
      {
        value: 'NavigationLightsAndShapes',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation lights & shapes',
        status: 'Required',
        // title: 'Navigation lights and shapes',
        groupTo: 'NavigationAndBridgeEquipment',
      },
      {
        value: 'ElectronicNavigationalAids',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electronic navigational aids',
        status: 'Required',
        // title: 'Electronic navigational aids',
        groupTo: 'NavigationAndBridgeEquipment',
      },
      {
        value: 'MainEngines',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Main engines',
        status: 'Required',
        // title: 'Main engines',
        groupTo: 'EngineRoomAndMachinery',
      },
      {
        value: 'AuxiliarySystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Auxiliary systems',
        status: 'Required',
        // title: 'Auxiliary systems',
        groupTo: 'EngineRoomAndMachinery',
      },
      {
        value: 'FuelAndOil',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel & oil',
        status: 'Required',
        // title: 'Fuel and oil systems',
        groupTo: 'EngineRoomAndMachinery',
      },
      {
        value: 'BilgeSystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bilge systems',
        status: 'Required',
        // title: 'Bilge systems',
        groupTo: 'EngineRoomAndMachinery',
      },
      {
        value: 'Power',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Power',
        status: 'Required',
        // title: 'Main and emergency power',
        groupTo: 'ElectricalSystems',
      },
      {
        value: 'BatteryMaintenance',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Battery maintenance',
        status: 'Required',
        // title: 'Battery maintenance',
        groupTo: 'ElectricalSystems',
      },
      {
        value: 'CircuitInspections',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Circuit inspections',
        status: 'Required',
        // title: 'Circuit inspections',
        groupTo: 'ElectricalSystems',
      },
      {
        value: 'MooringAndAnchoring',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Mooring & anchoring',
        status: 'Required',
        // title: 'Mooring and anchoring',
        groupTo: 'DeckOperations',
      },
      {
        value: 'CargoAndAccessEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Cargo & access equipment',
        status: 'Required',
        // title: 'Cargo and access equipment',
        groupTo: 'DeckOperations',
      },
      {
        value: 'HatchesAndWatertightDoors',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Hatches & watertight doors',
        status: 'Required',
        // title: 'Hatches and watertight doors',
        groupTo: 'DeckOperations',
      },
      {
        value: 'GalleyAppliances',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Galley appliances',
        status: 'Required',
        // title: 'Galley appliances',
        groupTo: 'AccommodationAndGalley',
      },
      {
        value: 'WasteManagement',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Waste management',
        status: 'Required',
        // title: 'Waste management',
        groupTo: 'AccommodationAndGalley',
      },
      {
        value: 'VentilationAndAirConditioning',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Ventilation & air conditioning',
        status: 'Required',
        // title: 'Ventilation and air conditioning',
        groupTo: 'AccommodationAndGalley',
      },
      {
        value: 'EmergencyReadiness',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Emergency readiness',
        status: 'Required',
        // title: 'Emergency readiness',
        groupTo: 'FinalChecks',
      },
      {
        value: 'EnvironmentalCompliance',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Environmental compliance',
        status: 'Required',
        // title: 'Environmental compliance',
        groupTo: 'FinalChecks',
      },
      {
        value: 'NavigationAndBridgeEquipment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigation & bridge equipment',
        // title: 'Navigation and bridge equipment',
        status: 'Required',
      },
      {
        value: 'EngineRoomAndMachinery',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Engine room & machinery',
        // title: 'Engine room and machinery',
        status: 'Required',
      },
      {
        value: 'ElectricalSystems',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Electrical systems',
        // title: 'Electrical systems',
        status: 'Required',
      },
      {
        value: 'DeckOperations',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Deck operations',
        // title: 'Deck Operations',
        status: 'Required',
      },
      {
        value: 'AccommodationAndGalley',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Accommodation and galley',
        // title: 'Accommodation and galley',
        status: 'Required',
      },
      {
        value: 'FinalChecks',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Final checks',
        // title: 'Final Checks',
        status: 'Required',
      },
    ],
  },
  // Event (Activity) types - EventType_LogBookComponent.
  {
    label: 'Activity Types',
    vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    category: 'Voyage',
    // title: 'Activity types',
    componentClass: 'EventType_LogBookComponent',
    items: [
      /*{
          value: 'ScheduledPassengerService',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Scheduled passenger service',
          // title: 'Scheduled passenger service',
          status: 'Required',
      },
      {
          value: 'WaterTaxiService',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Water taxi service',
          // title: 'Water taxi service',
          status: 'Required',
      },*/
      /* {
          value: 'TourismSightseeingService',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Tourism, sightseeing service',
          // title: 'Tourism, sightseeing service',
          status: 'Required',
      }, */
      /* {
          value: 'RecordMarineMammalEncounters',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Record marine and mammal encounters',
          // title: 'Record marine and mammal encounters',
          status: 'Required',
      }, */
      {
        value: 'PilotTransfer',
        vesselType: [0, 3],
        label: 'Pilot Transfer',
        status: 'Required',
      },
      {
        value: 'RefuellingBunkering',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Refuelling/bunkering',
        // title: 'Refuelling / Bunkering',
        status: 'Required',
      },
      {
        value: 'VesselRescue',
        vesselType: [0, 1, 3, 4, 7, 8, 9],
        label: 'Vessel incident',
        // title: 'Vessel Incident',
        status: 'Required',
        // forcedTitle: true,
      },
      {
        value: 'HumanRescue',
        vesselType: [0, 1, 3, 4, 7, 8, 9],
        label: 'Human incident',
        // title: 'Person Incident',
        status: 'Required',
        // forcedTitle: true,
      },
      {
        value: 'PassengerVehiclePickDrop',
        vesselType: [0, 2, 5, 6],
        label: 'Passenger/vehicle pickup/drop-off',
        // title: 'Passenger/vehicle pickup/drop off',
        status: 'Required',
      },
      {
        value: 'TripUpdate',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Trip Update',
        status: 'Required',
      },
      {
        value: 'PassengerVehiclePickDropLocation',
        vesselType: [0, 2, 5, 6],
        label: 'Location',
        // title: 'Location',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'PassengerVehiclePickDropArrival',
        vesselType: [0, 2, 5, 6],
        label: 'Arrival',
        // title: 'Arrival',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'PassengerVehiclePickDropDeparture',
        vesselType: [0, 2, 5, 6],
        label: 'Departure',
        // title: 'Departure',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'PassengerVehiclePickDropPaxPickDrop',
        vesselType: [0, 2, 5, 6],
        label: 'Pax pickup/drop off',
        // title: 'Pax Pickup/Drop off',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'PassengerVehiclePickDropVehiclePickDrop',
        vesselType: [0, 2, 5, 6],
        label: 'Vehicle pickup/drop off',
        // title: 'Vehicle Pickup/Drop off',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'PassengerVehiclePickDropOtherCargo',
        vesselType: [0, 2, 5, 6],
        label: 'Other cargo pickup/drop off',
        // title: 'Other Cargo Pickup/Drop off',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'DangerousGoods',
        vesselType: [0, 2, 5, 6],
        label: 'Dangerous goods',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      {
        value: 'DesignatedDangerousGoodsSailing',
        vesselType: [0, 2, 5, 6],
        label: 'Separate designated dangerous goods sailing',
        status: 'Required',
        groupTo: 'PassengerVehiclePickDrop',
      },
      // {
      //     value: 'DangerousGoodsSailing',
      //     vesselType: [0, 2, 5, 6],
      //     label: 'Designated dangerous goods sailing',
      // title: 'Designated Dangerous Goods Sailing',
      //     status: 'Required',
      //     classes: 'dangerous-goods-sailing',
      // },
      {
        value: 'VesselSecuredToWharf',
        vesselType: [0, 2, 5, 6],
        label: 'Vessel secured to wharf',
        // title: 'Vessel secured to wharf',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'BravoFlagRaised',
        vesselType: [0, 2, 5, 6],
        label: 'Bravo flag raised',
        // title: 'Bravo flag raised',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'TwoCrewLoadingVessel',
        vesselType: [0, 2, 5, 6],
        label: 'Two crew loading vessel',
        // title: 'Two crew loading vessel',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'FireHosesRiggedAndReady',
        vesselType: [0, 2, 5, 6],
        label: 'Fire hoses rigged and ready',
        // title: 'Fire hoses rigged and ready',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'NoSmokingSignagePosted',
        vesselType: [0, 2, 5, 6],
        label: 'No smoking signage posted',
        // title: 'No smoking signage posted',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'SpillKitAvailable',
        vesselType: [0, 2, 5, 6],
        label: 'Spill kit available',
        // title: 'Spill kit available',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'FireExtinguishersAvailable',
        vesselType: [0, 2, 5, 6],
        label: 'Fire extinguishers available',
        // title: 'Fire extinguishers available',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'DGDeclarationReceived',
        vesselType: [0, 2, 5, 6],
        label: 'DG declaration received',
        // title: 'DG declaration received',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'LoadPlanReceived',
        vesselType: [0, 2, 5, 6],
        label: 'Load plan received',
        // title: 'Load plan received',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
        classes: 'dangerous-goods-sailing',
      },
      {
        value: 'MSDSAvailable',
        vesselType: [0, 2, 5, 6],
        label: 'MSDS available for all dangerous goods carried',
        // title: 'MSDS available for all dangerous goods carried',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
      },
      {
        value: 'AnyVehiclesSecureToVehicleDeck',
        vesselType: [0, 2, 5, 6],
        label: 'Any vehicles secure to vehicle deck',
        // title: 'Any vehicles secure to vehicle deck',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
      },
      {
        value: 'SafetyAnnouncement',
        vesselType: [0, 2, 5, 6],
        label: 'Safety announcement includes reference to dangerous goods & no smoking',
        // title: 'Safety announcement includes reference to dangerous goods & no smoking',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
      },
      {
        value: 'VehicleStationaryAndSecure',
        vesselType: [0, 2, 5, 6],
        label: 'Vehicle stationary and secure prior to vehicle departing vessel',
        // title: 'Vehicle stationary and secure prior to vehicle departing vessel',
        status: 'Required',
        groupTo: 'DangerousGoodsSailing',
      },
      {
        value: 'IncludeTowingRiskEvaluation',
        vesselType: [0, 1],
        label: 'Include towing risk evaluation',
        // title: 'Include Towing risk evaluation',
        status: 'Required',
        groupTo: 'HumanRescue',
      },
      /* {
          value: 'IncidentsAccidentsNearMisses',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Incidents, accidents, and near misses',
          // title: 'Incidents, accidents and near misses',
          status: 'Required',
      }, */
      {
        value: 'RestrictedVisibility',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Restricted visibility',
        // title: 'Restricted visibility',
        status: 'Required',
      },
      {
        value: 'BarCrossing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Bar crossing',
        // title: 'Bar crossing',
        status: 'Required',
      },
      {
        value: 'PassengerArrival',
        vesselType: [0, 1, 4, 7, 8, 9],
        label: 'Passenger arrival',
        // title: 'Arrival',
        status: 'Required',
      },
      {
        value: 'PassengerDeparture',
        vesselType: [0, 1, 4, 7, 8, 9],
        label: 'Passenger departure',
        // title: 'Departure',
        status: 'Required',
      },
      {
        value: 'CrewTraining',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew training',
        // title: 'Training / Drills',
        status: 'Required',
      },
      {
        value: 'EventSupernumerary',
        vesselType: [0, 1, 2, 4, 5, 6, 7, 8, 9],
        label: 'Supernumerary',
        // title: 'Supernumerary',
        status: 'Required',
      },
      {
        value: 'InfringementNotices',
        vesselType: [0, 1],
        label: 'Infringement notices',
        // title: 'Infringement Notices',
        status: 'Required',
      },
      {
        value: 'TaskingStartUnderway',
        vesselType: [0, 1],
        label: 'Tasking start/underway',
        // title: 'Tasking start / underway',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TaskingOnScene',
        vesselType: [0, 1],
        label: 'Tasking on scene',
        // title: 'Tasking on scene',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TaskingOnTow',
        vesselType: [0, 1],
        label: 'Tasking on tow',
        // title: 'Tasking on tow',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TaskingPaused',
        vesselType: [0, 1],
        label: 'Tasking paused',
        // title: 'Tasking paused',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TaskingResumed',
        vesselType: [0, 1],
        label: 'Tasking resumed',
        // title: 'Tasking resumed',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TaskingComplete',
        vesselType: [0, 1],
        label: 'Tasking complete',
        // title: 'Tasking complete',
        status: 'Required',
        type: 'RescueSubCategory',
        classes: 'hidden',
      },
      /* {
          value: 'Other',
          vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
          label: 'Other (description)',
          status: 'Required',
      }, */
      {
        value: 'PassengerArrival_FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        // title: 'Fuel Level',
        status: 'Required',
        groupTo: 'PassengerArrival',
      },
      {
        value: 'PassengerArrival_Pax',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax',
        // title: 'Pax',
        status: 'Required',
        groupTo: 'PassengerArrival',
      },
      {
        value: 'PassengerDeparture_FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        // title: 'Fuel Level',
        status: 'Required',
        groupTo: 'PassengerDeparture',
      },
      {
        value: 'PassengerDeparture_Pax',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax',
        // title: 'Pax',
        status: 'Required',
        groupTo: 'PassengerDeparture',
      },
      {
        value: 'WaterTaxiService_FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        // title: 'Fuel Level',
        status: 'Required',
        groupTo: 'WaterTaxiService',
      },
      {
        value: 'WaterTaxiService_Pax',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax',
        // title: 'Pax',
        status: 'Required',
        groupTo: 'WaterTaxiService',
      },
      {
        value: 'ScheduledPassengerService_FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        // title: 'Fuel Level',
        status: 'Required',
        groupTo: 'ScheduledPassengerService',
      },
      {
        value: 'ScheduledPassengerService_Pax',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Pax',
        // title: 'Pax',
        status: 'Required',
        groupTo: 'ScheduledPassengerService',
      },
      {
        value: 'BarCrossing_Location',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Location',
        // title: 'Start Location',
        // forcedTitle: 'Start Location',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_EndLocation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Location',
        // title: 'End Location',
        // forcedTitle: 'End Location',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_Time',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time',
        // title: 'Start Crossing Time',
        // forcedTitle: 'Start Crossing Time',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_EndTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time',
        // title: 'End Crossing Time',
        // forcedTitle: 'End Crossing Time',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_StopAssessPlan',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Stop assess plan',
        // title: 'Stop Assess Plan',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_CrewBriefing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew briefing',
        // title: 'Crew Briefing',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_Weather',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Weather',
        // title: 'Weather',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_Stability',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Stability',
        // title: 'Stability',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_WaterTightness',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Water tightness',
        // title: 'Water Tightness',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_LifeJackets',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Life jackets',
        // title: 'Life Jackets',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_LookoutPosted',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lookout posted',
        // title: 'Lookout Posted',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'BarCrossing_Report',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Report',
        // title: 'Report',
        status: 'Required',
        groupTo: 'BarCrossing',
      },
      {
        value: 'RestrictedVisibility_StartLocation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Start location',
        // title: 'Start Location',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_EndLocation',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'End location',
        // title: 'End Location',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_CrossingTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crossing time',
        // title: 'Crossing Time',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_EstSafeSpeed',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Estimated safe speed',
        // title: 'Estimate Safe Speed',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_StopAssessPlan',
        vesselType: [0, 1, 3, 4, 7, 8, 9],
        label: 'Stop assess plan',
        // title: 'Stop Assess Plan',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_CrewBriefing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crew briefing',
        // title: 'Crew Briefing',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_NavLights',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Navigational lights',
        // title: 'Nav Lights',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_SoundSignals',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sound signals',
        // title: 'Sound Signals',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_Lookout',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Lookout',
        // title: 'Lookout',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_SoundSignal',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Sound signal',
        // title: 'Sound Signal',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_RadarWatch',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Radar watch',
        // title: 'Radar Watch',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_RadioWatch',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Radio watch',
        // title: 'Radio Watch',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_CrossedTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Crossed time',
        // title: 'Crossed Time',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_ApproxSafeSpeed',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Approximate safe speed',
        // title: 'Approx Safe Speed',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'RestrictedVisibility_Report',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Report',
        // title: 'Report',
        status: 'Required',
        groupTo: 'RestrictedVisibility',
      },
      {
        value: 'CrewTraining_FuelLevel',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Fuel level',
        // title: 'Fuel Level',
        status: 'Required',
        groupTo: 'CrewTraining',
      },
      {
        value: 'CrewTraining_Location',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Location',
        // title: 'Location',
        status: 'Required',
        groupTo: 'CrewTraining',
      },
      {
        value: 'CrewTraining_StartTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Start time',
        // title: 'Start Time',
        status: 'Required',
        groupTo: 'CrewTraining',
      },
      {
        value: 'CrewTraining_FinishTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Finish time',
        // title: 'Finish Time',
        status: 'Required',
        groupTo: 'CrewTraining',
      },
      {
        value: 'Supernumerary_FirstName',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'First name',
        // title: 'First Name',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_Surname',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Surname',
        // title: 'Surname',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_Signature',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Signature',
        // title: 'Signature',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_Guests',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Number of guests',
        // title: 'Number of Guests',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_GuestBriefing',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Guest briefing completed',
        // title: 'Guest Briefing Completed',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_BriefingTime',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time of briefing',
        // title: 'Time of Briefing',
        status: 'Required',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'Supernumerary_Policies',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Policies',
        // title: 'Policies',
        status: 'Required',
        fieldType: 'files',
        groupTo: 'EventSupernumerary',
      },
      {
        value: 'ConductSAP',
        vesselType: [0, 1],
        label: 'Conduct SAP',
        // title: 'ConductSAP',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'InvestigateNatureOfIssue',
        vesselType: [0, 1],
        label: 'Investigate nature of issue',
        // title: 'InvestigateNatureOfIssue',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'EveryoneOnBoardOk',
        vesselType: [0, 1],
        label: 'Everyone on board OK',
        // title: 'EveryoneOnBoardOk',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'RudderToMidshipsAndTrimmed',
        vesselType: [0, 1],
        label: 'Rudder to midships & trimmed',
        // title: 'RudderToMidshipsAndTrimmed',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'LifejacketsOn',
        vesselType: [0, 1],
        label: 'Life jackets on',
        // title: 'LifejacketsOn',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'CommunicationsEstablished',
        vesselType: [0, 1],
        label: 'Communications established',
        // title: 'CommunicationsEstablished',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'SecureAndSafeTowing',
        vesselType: [0, 1],
        label: 'Secure & safe towing',
        // title: 'SecureAndSafeTowing',
        status: 'Required',
        type: 'TowingSubCategory',
        classes: 'hidden',
      },
      {
        value: 'TripUpdate_Time',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Time',
        status: 'Required',
        groupTo: 'TripUpdate',
      },
      {
        value: 'TripUpdate_Title',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Title',
        status: 'Required',
        groupTo: 'TripUpdate',
      },
      {
        value: 'TripUpdate_Location',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Location',
        status: 'Required',
        groupTo: 'TripUpdate',
      },
      {
        value: 'TripUpdate_Comments',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Comments',
        status: 'Required',
        groupTo: 'TripUpdate',
      },
      {
        value: 'TripUpdate_Attachment',
        vesselType: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        label: 'Attachment',
        status: 'Required',
        groupTo: 'TripUpdate',
      },
    ],
  },
]