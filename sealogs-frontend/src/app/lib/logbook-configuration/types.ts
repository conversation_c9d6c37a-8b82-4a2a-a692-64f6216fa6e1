export type LogBookComponent =
  | 'CrewMembers_LogBookComponent'
  | 'CrewTraining_LogBookComponent'
  | 'Engine_LogBookComponent'
  | 'Engineer_LogBookComponent'
  | 'Fuel_LogBookComponent'
  | 'Ports_LogBookComponent'
  | 'Supernumerary_LogBookComponent'
  | 'TripReport_LogBookComponent'
  | 'VesselDailyCheck_LogBookComponent'
  | 'Weather_LogBookComponent'
  | 'VoyageSummary_LogBookComponent'
  | 'CrewWelfare_LogBookComponent'
  | 'LogBookSignOff_LogBookComponent'
  | 'EventType_LogBookComponent';


export type LogBookConfigurationField = {
  value: string;
  vesselType: number[];
  label: string;
  status: string;
  description?: string;
  fieldSet?: string;
  groupTo?: string;
  tab?: string;
  level?: number;
  classes?: string;
  type?: string;
  fieldType?: string;
  noCheck?: boolean
  hasDynamicChildren?: boolean,
};

export type LogBookConfiguration = {
  label: string;
  vesselType: number[];
  category: string;
  sortOrder?: number;
  componentClass: string;
  items: LogBookConfigurationField[];
  subCategory?: boolean;
};