import { useCallback, useState, useMemo, useRef, useEffect } from 'react'
import { UnifiedTrainingData } from '../utils/crew-training-utils'

// Performance optimization: Cache filter results to avoid redundant calculations
const filterCache = new Map<string, UnifiedTrainingData[]>()
const CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues

// Helper function to generate cache key from filter and data
const generateCacheKey = (filter: UnifiedSearchFilter, dataLength: number, dataHash: string): string => {
    return JSON.stringify({ filter, dataLength, dataHash })
}

// Helper function to generate a simple hash from data array
const generateDataHash = (data: UnifiedTrainingData[]): string => {
    if (!data || data.length === 0) return 'empty'
    // Use first and last item IDs plus length for a simple hash
    return `${data[0]?.id}-${data[data.length - 1]?.id}-${data.length}`
}

export interface UnifiedSearchFilter {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

/**
 * Hook for filtering unified training data on the client side
 * Works with merged training data from mergeAndSortCrewTrainingData
 */
export function useUnifiedTrainingFilters(opts: {
    initialFilter: UnifiedSearchFilter
    unifiedData: UnifiedTrainingData[]
}) {
    const { initialFilter, unifiedData } = opts



    const [filter, setFilter] = useState<UnifiedSearchFilter>(() => {
        return {}
    })

    const [debouncedFilter, setDebouncedFilter] = useState<UnifiedSearchFilter>(() => {
        return initialFilter
    })
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Debounce filter changes to improve performance during rapid changes
    useEffect(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current)
        }

        debounceTimeoutRef.current = setTimeout(() => {
            setDebouncedFilter(filter)
        }, 300) // 300ms debounce delay

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current)
            }
        }
    }, [filter, debouncedFilter])

    const handleFilterChange = useCallback(
        ({ type, data }: { type: string; data: any }) => {
            const next: UnifiedSearchFilter = { ...filter }

            /* ---- vessel ------------------------------------------------------- */
            if (type === 'vessel') {
                if (Array.isArray(data) && data.length) {
                    next.vesselID = { in: data.map((d) => +d.value) }
                } else if (data && !Array.isArray(data)) {
                    next.vesselID = { eq: +data.value }
                } else {
                    delete next.vesselID
                }
            }

            /* ---- trainingType ------------------------------------------------- */
            if (type === 'trainingType') {
              

                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.trainingTypes = { id: { in: mappedValues } }
                    
                } else if (data && !Array.isArray(data)) {
                    const containsValue = +data.value
                    next.trainingTypes = { id: { contains: containsValue } }
                    
                } else {
                    delete next.trainingTypes
                    
                }
            }

            /* ---- trainer ------------------------------------------------------ */
            if (type === 'trainer') {
             
                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.trainer = { id: { in: mappedValues } }
                   
                } else if (data && !Array.isArray(data)) {
                    const eqValue = +data.value
                    next.trainer = { id: { eq: eqValue } }
                
                } else {
                    delete next.trainer
                  
                }
            }

            /* ---- member ------------------------------------------------------- */
            if (type === 'member') {
                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.members = { id: { in: mappedValues } }
                } else if (data && !Array.isArray(data)) {
                    const eqValue = +data.value
                    next.members = { id: { eq: eqValue } }
                } else {
                    delete next.members
                }
            }

            /* ---- dateRange ---------------------------------------------------- */
            if (type === 'dateRange') {
                if (data?.startDate && data?.endDate) {
                    next.date = { gte: data.startDate, lte: data.endDate }
                } else {
                    delete next.date
                }
            }

            /* ---- category ----------------------------------------------------- */
            if (type === 'category') {
                if (data && data !== 'all') {
                    next.category = data
                } else {
                    delete next.category
                }
            }

            setFilter(next)
        },
        [filter],
    )

    // Performance-optimized client-side filtering of unified data
    const filteredData = useMemo(() => {
        if (!unifiedData || !Array.isArray(unifiedData)) {
            return []
        }

        // Performance optimization: Check cache first (temporarily disabled for debugging)
        // const dataHash = generateDataHash(unifiedData)
        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)
        // if (filterCache.has(cacheKey)) {
        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')
        //     return filterCache.get(cacheKey)!
        // }



        // Optimized filtering with early returns for better performance
        const filtered = unifiedData.filter((item: UnifiedTrainingData) => {
     

            // Category filter
            if (debouncedFilter.category && debouncedFilter.category !== 'all') {
                if (item.category !== debouncedFilter.category) {
             
                    return false
                }
            }

            // Vessel filter
            if (debouncedFilter.vesselID) {
                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers
                const itemVesselIdNum = typeof item.vesselID === 'string' ? parseInt(item.vesselID, 10) : item.vesselID

         

                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {
               
                    return false
                }
                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {
             
                    return false
                }
            }

            // Training type filter
            if (debouncedFilter.trainingTypes) {
                // For completed training sessions, check all training types in the array
                if (item.category === 'completed' && item.originalData?.trainingTypes?.nodes) {
                    const trainingTypeIds = item.originalData.trainingTypes.nodes.map((tt: any) =>
                        typeof tt.id === 'string' ? parseInt(tt.id, 10) : tt.id
                    )

                    if (debouncedFilter.trainingTypes.id?.contains && !trainingTypeIds.includes(debouncedFilter.trainingTypes.id.contains)) {
                        return false
                    }
                    if (debouncedFilter.trainingTypes.id?.in && !debouncedFilter.trainingTypes.id.in.some(id => trainingTypeIds.includes(id))) {
                        return false
                    }
                } else {
                    // For overdue/upcoming training sessions, use existing single training type logic
                    const trainingTypeId = item.trainingTypeID || item.trainingType?.id
                    // Convert to number for comparison since filter values are numbers but data might be strings
                    const trainingTypeIdNum = typeof trainingTypeId === 'string' ? parseInt(trainingTypeId, 10) : trainingTypeId

                    if (debouncedFilter.trainingTypes.id?.contains && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {
                        return false
                    }
                    if (debouncedFilter.trainingTypes.id?.in && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {
                        return false
                    }
                }
            }

            // Trainer filter (for completed training sessions)
            if (debouncedFilter.trainer) {
           

                if (item.originalData) {
                    const trainerId = item.originalData.trainerID || item.originalData.trainer?.id
                    // Convert to number for comparison since filter values are numbers but data might be strings
                    const trainerIdNum = typeof trainerId === 'string' ? parseInt(trainerId, 10) : trainerId


                    if (trainerIdNum) {
                        if (debouncedFilter.trainer.id?.eq && trainerIdNum !== debouncedFilter.trainer.id.eq) {
                   
                            return false
                        }
                        if (debouncedFilter.trainer.id?.in && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {
                       
                            return false
                        }
                    } else if (debouncedFilter.trainer.id) {
                  
                        return false
                    }
                } else {
                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields
                    const itemAsAny = item as any // Type assertion to access potential trainer fields
              

                    // Check if item has trainer info in other fields
                    const itemTrainerId = itemAsAny.trainerID || itemAsAny.trainer?.id
                    const itemTrainerIdNum = typeof itemTrainerId === 'string' ? parseInt(itemTrainerId, 10) : itemTrainerId

                    if (itemTrainerIdNum) {
                        if (debouncedFilter.trainer.id?.eq && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {
                      
                            return false
                        }
                        if (debouncedFilter.trainer.id?.in && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {
                     
                            return false
                        }
                    } else if (debouncedFilter.trainer.id) {
                        // Special handling for overdue/upcoming training sessions:
                        // Include them in results since they don't have trainers assigned yet
                        if (item.category === 'overdue' || item.category === 'upcoming') {
                   
                            // Don't filter out overdue/upcoming items - they should be included
                        } else {
                  
                            return false
                        }
                    }
                }
            }

            // Member filter - only apply if explicitly set by user
            if (debouncedFilter.members && (debouncedFilter.members.id?.eq || debouncedFilter.members.id?.in)) {


                if (item.members && item.members.length > 0) {
                    // Convert member IDs to numbers for consistent comparison
                    const memberIds = item.members.map((member: any) => {
                        const id = member.id
                        return typeof id === 'string' ? parseInt(id, 10) : id
                    })


                    if (debouncedFilter.members.id?.eq && !memberIds.includes(debouncedFilter.members.id.eq)) {

                        return false
                    }
                    if (debouncedFilter.members.id?.in && debouncedFilter.members.id.in.length > 0) {
                        const hasMatchingMember = debouncedFilter.members.id.in.some(id => memberIds.includes(id))
                        if (!hasMatchingMember) {

                            return false
                        }
                    }
                } else {
                    // If item has no members but filter is set, filter it out
                    return false
                }
            }

            // Date filter
            if (debouncedFilter.date && item.dueDate) {
                const itemDate = new Date(item.dueDate)
                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {
                    return false
                }
                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {
                    return false
                }
            }

       
            return true
        })

        return filtered
    }, [unifiedData, debouncedFilter])

    return {
        filter,
        setFilter,
        handleFilterChange,
        filteredData
    }
}
