'use client'

import { useMemo } from 'react'
import Link from 'next/link'
import { format } from 'date-fns'
import {
    Avatar,
    AvatarFallback,
    Button,
    getCrewInitials,
    P,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
    Separator,
    StatusBadge,
} from '@/components/ui'
import { Label } from '@/components/ui/label'
import { LocationModal } from '../vessels/list'
import VesselIcon from '../vessels/vesel-icon'
import { cn } from '@/app/lib/utils'
import { createColumns, ExtendedColumnDef } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { FilteredTable } from '@/components/filteredTable'
import {
    mergeAndSortCrewTrainingData,
    UnifiedTrainingData,
} from '@/app/ui/crew-training/utils/crew-training-utils'
import { useMediaQuery } from '@reactuses/core'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

// Helper function to format dates using date-fns
const formatDate = (dateString: any) => {
    if (!dateString) return ''
    try {
        const date = new Date(dateString)
        return format(date, 'dd/MM/yy')
    } catch {
        return ''
    }
}

// Row status evaluator for highlighting overdue/upcoming rows
type RowStatus = 'overdue' | 'upcoming' | 'normal'

const getRowStatus = (rowData: UnifiedTrainingData): RowStatus => {
    if (rowData.status.isOverdue) {
        return 'overdue'
    }
    if (rowData.status.dueWithinSevenDays) {
        return 'upcoming'
    }
    return 'normal'
}

// Status-based color classes for training titles
const getStatusColorClasses = (training: UnifiedTrainingData) => {
    if (training.status.isOverdue) {
        return 'text-destructive/80 hover:text-destructive'
    }
    if (training.status.dueWithinSevenDays) {
        return 'text-warning/80 hover:text-warning'
    }
    return 'hover:text-curious-blue-400'
}

// Mobile card component for unified training data
interface UnifiedMobileTrainingCardProps {
    data: UnifiedTrainingData
}

const UnifiedMobileTrainingCard = ({
    data,
}: UnifiedMobileTrainingCardProps) => {
    const bp = useBreakpoints()
    const isCompleted = data.category === 'completed'
    const isOverdue = data.category === 'overdue'
    const members = data.members || []
    const trainingTitle = data.trainingType?.title || ''

    return (
        <div className="w-full space-y-2.5 tablet-md:border-none py-3">
            {/* Date/Title - Always visible */}
            <div className="flex-1 flex flex-wrap justify-between items-center">
                {isCompleted ? (
                    <Link
                        href={`/crew-training/info?id=${data.id}`}
                        className="font-semibold text-base hover:text-primary">
                        {formatDate(data.dueDate)}
                    </Link>
                ) : (
                    <div
                        className={cn(
                            'font-semibold text-base',
                            getStatusColorClasses(data),
                        )}>
                        {formatDate(data.dueDate)}
                    </div>
                )}

                {/* Status - Show when landscape is hidden (portrait mode) */}
                {!bp.landscape && (
                    <StatusBadge
                        isOverdue={data.status.isOverdue}
                        isUpcoming={data.status.dueWithinSevenDays}
                        label={data.status.label || data.category}
                    />
                )}
            </div>

            {/* Training Type - Show when tablet-md column is hidden (< 768px) */}
            {!bp.laptop && (
                <div className="text-sm line-clamp-2">
                    {isCompleted
                        ? data.originalData?.trainingTypes?.nodes
                            ? data.originalData.trainingTypes.nodes
                                  .map((item: any) => item.title)
                                  .join(', ')
                            : trainingTitle
                        : trainingTitle}
                </div>
            )}

            {/* Vessel - Show when landscape column is hidden (< 1024px) */}
            {!bp['tablet-md'] && (
                <div className="flex flex-wrap gap-2.5 items-center">
                    <Label className="text-sm m-0 text-muted-foreground">
                        {isCompleted ? 'Location:' : 'Vessel:'}
                    </Label>
                    <div className="flex items-center relative gap-2">
                        {data.vessel && (
                            <Tooltip mobileClickable>
                                <TooltipTrigger mobileClickable>
                                    <div className="size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8">
                                        <VesselIcon vessel={data.vessel} />
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    {data.vessel?.title || 'Vessel'}
                                </TooltipContent>
                            </Tooltip>
                        )}
                        <LocationModal
                            vessel={data.vessel}
                            className="absolute -top-1.5 -right-2.5"
                            iconClassName="size-6"
                        />
                    </div>
                </div>
            )}

            {/* Crew - Show when laptop column is hidden (< 1280px) */}
            {!bp['tablet-lg'] && (
                <div className="flex gap-1">
                    {members
                        .slice(0, bp['tablet-md'] ? 8 : 6)
                        .map((member: any) => (
                            <Tooltip key={member.id} mobileClickable>
                                <TooltipTrigger mobileClickable>
                                    <Avatar
                                        size="sm"
                                        variant={
                                            !isCompleted && isOverdue
                                                ? 'destructive'
                                                : 'secondary'
                                        }>
                                        <AvatarFallback className="text-sm">
                                            {getCrewInitials(
                                                member.firstName,
                                                member.surname,
                                            )}
                                        </AvatarFallback>
                                    </Avatar>
                                </TooltipTrigger>
                                <TooltipContent>
                                    {member.firstName} {member.surname ?? ''}
                                </TooltipContent>
                            </Tooltip>
                        ))}
                    {members.length > (bp['tablet-md'] ? 8 : 6) && (
                        <Popover>
                            <PopoverTrigger className="w-fit" asChild>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-fit h-8">
                                    +
                                    {members.length - (bp['tablet-md'] ? 8 : 6)}{' '}
                                    more
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-64">
                                <div className="p-3 max-h-64 overflow-auto">
                                    <div className="space-y-2">
                                        {members
                                            .slice(bp['tablet-md'] ? 8 : 6)
                                            .map((remainingMember: any) => (
                                                <div
                                                    key={remainingMember.id}
                                                    className="text-sm flex items-center gap-2">
                                                    <Avatar
                                                        size="xs"
                                                        variant="secondary">
                                                        <AvatarFallback className="text-xs">
                                                            {getCrewInitials(
                                                                remainingMember.firstName,
                                                                remainingMember.surname,
                                                            )}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    {`${remainingMember.firstName ?? ''} ${remainingMember.surname ?? ''}`}
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            </PopoverContent>
                        </Popover>
                    )}
                </div>
            )}
        </div>
    )
}

// Main unified training table component
interface UnifiedTrainingTableProps {
    trainingSessionDues?: any[]
    completedTrainingList?: any[]
    unifiedData?: UnifiedTrainingData[] // Add support for pre-filtered unified data
    getVesselWithIcon?: (id: any, vessel: any) => any
    includeCompleted?: boolean
    memberId?: number
    isVesselView?: boolean
    showToolbar?: boolean
    pageSize?: number
}

export const UnifiedTrainingTable = ({
    trainingSessionDues = [],
    completedTrainingList = [],
    unifiedData: preFilteredData,
    getVesselWithIcon,
    includeCompleted = true,
    memberId,
    isVesselView = false,
    showToolbar = false,
    pageSize,
}: UnifiedTrainingTableProps) => {
    const isWide = useMediaQuery('(min-width: 720px)')

    // Use pre-filtered data if available, otherwise merge and sort data
    const unifiedData = useMemo(() => {
        if (preFilteredData && Array.isArray(preFilteredData)) {
            return preFilteredData
        }

        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [
        preFilteredData,
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Determine if we have mixed data types or primarily one type
    const hasOverdueOrUpcoming = unifiedData.some(
        (item) => item.category === 'overdue' || item.category === 'upcoming',
    )
    const hasCompleted = unifiedData.some(
        (item) => item.category === 'completed',
    )

    // Create unified column structure for all training data types
    const getUnifiedColumns = () => {
        return [
            // Mobile column - shows training card on mobile, adapts header based on data
            {
                accessorKey: 'title',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Date" />
                ),
                cellAlignment: 'left' as const,
                cellClassName: 'w-ful xs:w-auto',
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original
                    return <UnifiedMobileTrainingCard data={training} />
                },
                sortingFn: (rowA: any, rowB: any) => {
                    // Sort by category priority first, then by date
                    const trainingA: UnifiedTrainingData = rowA.original
                    const trainingB: UnifiedTrainingData = rowB.original

                    const priorityA =
                        trainingA.category === 'overdue'
                            ? 1
                            : trainingA.category === 'upcoming'
                              ? 2
                              : 3
                    const priorityB =
                        trainingB.category === 'overdue'
                            ? 1
                            : trainingB.category === 'upcoming'
                              ? 2
                              : 3

                    if (priorityA !== priorityB) {
                        return priorityA - priorityB
                    }

                    const dateA = new Date(trainingA.dueDate).getTime()
                    const dateB = new Date(trainingB.dueDate).getTime()

                    return trainingA.category === 'completed'
                        ? dateB - dateA
                        : dateA - dateB
                },
            },
            // Training Type column - shows training types for all data types
            {
                accessorKey: 'trainingType',
                cellAlignment: 'left' as const,
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader
                        column={column}
                        title="Training/drill"
                    />
                ),
                breakpoint: 'laptop' as const,
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original
                    const isCompleted = training.category === 'completed'

                    return (
                        <P>
                            {isCompleted
                                ? training.originalData?.trainingTypes?.nodes
                                    ? training.originalData.trainingTypes.nodes
                                          .map((item: any) => item.title)
                                          .join(', ')
                                    : training.trainingType?.title || ''
                                : training.trainingType?.title || ''}
                        </P>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA =
                        rowA?.original?.originalData?.trainingTypes?.nodes?.[0]
                            ?.title ||
                        rowA?.original?.trainingType?.title ||
                        ''
                    const valueB =
                        rowB?.original?.originalData?.trainingTypes?.nodes?.[0]
                            ?.title ||
                        rowB?.original?.trainingType?.title ||
                        ''
                    return valueA.localeCompare(valueB)
                },
            },
            // Vessel column - shows vessel information for all data types
            {
                accessorKey: 'vessel',
                cellAlignment: 'left' as const,
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader
                        column={column}
                        title={isVesselView ? '' : 'Where'}
                    />
                ),
                breakpoint: 'tablet-md' as const,
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original

                    if (isVesselView) {
                        return <div></div>
                    }

                    return (
                        <div className="flex items-center gap-2">
                            {training.vessel && (
                                <Tooltip mobileClickable>
                                    <TooltipTrigger mobileClickable>
                                        <div className="size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8">
                                            <VesselIcon
                                                vessel={training.vessel}
                                            />
                                        </div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        {training.vessel?.title || 'Vessel'}
                                    </TooltipContent>
                                </Tooltip>
                            )}
                            <span className="text-sm hidden laptop:block text-nowrap">
                                {training.vessel?.title ||
                                    training.originalData
                                        ?.trainingLocationType ||
                                    ''}
                            </span>
                            <LocationModal
                                vessel={training.vessel}
                                iconClassName="size-8"
                            />
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA = rowA?.original?.vessel?.title || ''
                    const valueB = rowB?.original?.vessel?.title || ''
                    return valueA.localeCompare(valueB)
                },
            },
            // Crew column - shows crew members for all data types
            {
                accessorKey: 'crew',
                cellAlignment: 'left' as const,
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Who" />
                ),
                breakpoint: 'tablet-lg' as const,
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original
                    const members =
                        training.originalData?.members?.nodes ||
                        training.members ||
                        []

                    return isWide ? (
                        <div className="flex gap-1 items-center">
                            {members
                                .slice(0, 3)
                                .map((member: any, index: number) => (
                                    <Tooltip
                                        key={member.id || index}
                                        mobileClickable>
                                        <TooltipTrigger mobileClickable>
                                            <Avatar
                                                size="sm"
                                                variant={
                                                    training.status.isOverdue
                                                        ? 'destructive'
                                                        : 'secondary'
                                                }>
                                                <AvatarFallback className="text-sm">
                                                    {getCrewInitials(
                                                        member.firstName,
                                                        member.surname,
                                                    )}
                                                </AvatarFallback>
                                            </Avatar>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            {member.firstName}{' '}
                                            {member.surname ?? ''}
                                        </TooltipContent>
                                    </Tooltip>
                                ))}
                            {members.length > 3 && (
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="h-8 px-2 text-xs">
                                            +{members.length - 3} more
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-64">
                                        <div className="p-3 max-h-64 overflow-auto">
                                            <div className="space-y-2">
                                                {members
                                                    .slice(3)
                                                    .map(
                                                        (
                                                            remainingMember: any,
                                                        ) => (
                                                            <div
                                                                key={
                                                                    remainingMember.id
                                                                }
                                                                className="text-sm flex items-center gap-2">
                                                                <Avatar
                                                                    size="xs"
                                                                    variant="secondary">
                                                                    <AvatarFallback className="text-xs">
                                                                        {getCrewInitials(
                                                                            remainingMember.firstName,
                                                                            remainingMember.surname,
                                                                        )}
                                                                    </AvatarFallback>
                                                                </Avatar>
                                                                {`${remainingMember.firstName ?? ''} ${remainingMember.surname ?? ''}`}
                                                            </div>
                                                        ),
                                                    )}
                                            </div>
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            )}
                        </div>
                    ) : (
                        <div
                            className={cn(
                                '!rounded-full size-10 flex items-center justify-center text-sm font-medium',
                                training.status?.class,
                            )}>
                            {members.length}
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const membersA =
                        rowA?.original?.originalData?.members?.nodes ||
                        rowA?.original?.members ||
                        []
                    const membersB =
                        rowB?.original?.originalData?.members?.nodes ||
                        rowB?.original?.members ||
                        []
                    const valueA =
                        `${membersA?.[0]?.firstName ?? ''} ${membersA?.[0]?.surname ?? ''}` ||
                        ''
                    const valueB =
                        `${membersB?.[0]?.firstName ?? ''} ${membersB?.[0]?.surname ?? ''}` ||
                        ''
                    return valueA.localeCompare(valueB)
                },
            },
            // Trainer column - shows trainer for completed training, dash for others
            {
                accessorKey: 'trainer',
                cellAlignment: 'center' as const,
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Trainer" />
                ),
                breakpoint: 'tablet-md' as const,
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original
                    const trainer = training.originalData?.trainer

                    if (!trainer || training.category !== 'completed') {
                        return (
                            <div className="text-center text-muted-foreground">
                                -
                            </div>
                        )
                    }

                    return (
                        <div className="text-nowrap">
                            <Tooltip mobileClickable>
                                <TooltipTrigger mobileClickable>
                                    <Avatar size="sm" variant={'secondary'}>
                                        <AvatarFallback className="text-sm">
                                            {getCrewInitials(
                                                trainer.firstName,
                                                trainer.surname,
                                            )}
                                        </AvatarFallback>
                                    </Avatar>
                                </TooltipTrigger>
                                <TooltipContent>
                                    {trainer.firstName} {trainer.surname ?? ''}
                                </TooltipContent>
                            </Tooltip>
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA =
                        `${rowA?.original?.originalData?.trainer?.firstName || ''} ${rowA?.original?.originalData?.trainer?.surname || ''}` ||
                        ''
                    const valueB =
                        `${rowB?.original?.originalData?.trainer?.firstName || ''} ${rowB?.original?.originalData?.trainer?.surname || ''}` ||
                        ''
                    return valueA.localeCompare(valueB)
                },
            },
            // Status column - shows status badge at the end of the row
            {
                accessorKey: 'status',
                cellAlignment: 'right' as const,
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Status" />
                ),
                breakpoint: 'landscape' as const,
                cell: ({ row }: { row: any }) => {
                    const training: UnifiedTrainingData = row.original
                    return (
                        <StatusBadge
                            isOverdue={training.status.isOverdue}
                            isUpcoming={training.status.dueWithinSevenDays}
                            label={training.status.label || training.category}
                        />
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA = rowA?.original?.status?.label || ''
                    const valueB = rowB?.original?.status?.label || ''
                    return valueA.localeCompare(valueB)
                },
            },
        ]
    }

    // Create table columns
    const columns = useMemo<ExtendedColumnDef<UnifiedTrainingData, any>[]>(
        () =>
            createColumns(getUnifiedColumns()) as ExtendedColumnDef<
                UnifiedTrainingData,
                any
            >[],
        [hasOverdueOrUpcoming, hasCompleted, isVesselView, isWide],
    )

    if (!unifiedData?.length) {
        return (
            <div className="text-center py-8 text-muted-foreground">
                No training data available
            </div>
        )
    }

    return (
        <FilteredTable
            columns={columns}
            data={unifiedData}
            showToolbar={showToolbar}
            rowStatus={getRowStatus}
            pageSize={pageSize || 20}
        />
    )
}

export default UnifiedTrainingTable
