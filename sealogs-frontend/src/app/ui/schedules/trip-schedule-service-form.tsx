'use client'
import { getVesselList } from '@/app/lib/actions'
import {
    DeleteTripScheduleServices,
    UpdateTripScheduleService,
} from '@/app/lib/graphQL/mutation'
import { ReadOneTripScheduleService } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { isEmpty, trim } from 'lodash'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

// Shadcn UI components
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'

import { Combobox } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { H2 } from '@/components/ui/typography'
import { useToast } from '@/hooks/use-toast'

// Lucide icons
import { Save, Trash2, X } from 'lucide-react'
import { AlertDialogNew, Button } from '@/components/ui'
const TripScheduleServiceForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const id = searchParams.get('id') ?? 0
    const [tss, setTss] = useState({} as any)
    const [vessels, setVessels] = useState<any>([])
    const [selectedVessels, setSelectedVessels] = useState<any>([])
    const [tripReportSchedules, setTripReportSchedules] = useState<any>([])
    const [selectedTripReportSchedules, setSelectedTripReportSchedules] =
        useState<any>([])
    const [openConfirmDeleteDialog, setOpenConfirmDeleteDialog] =
        useState(false)
    const handleOnChangeTitle = (e: any) => {
        setTss({ ...tss, title: e.target.value })
    }
    const handleOnChangeCode = (e: any) => {
        setTss({ ...tss, code: e.target.value })
    }
    const handleSetSharePaxData = (checked: boolean) => {
        setTss({ ...tss, sharePaxData: checked })
    }
    const handleOnChangeTransitID = (e: any) => {
        setTss({ ...tss, transitID: e.target.value })
    }
    const handleOnChangePublicDescription = (e: any) => {
        setTss({ ...tss, publicDescription: e.target.value })
    }
    const handleVesselChange = (e: any) => {
        setSelectedVessels(e)
        setTss({
            ...tss,
            vehicles: e.map((v: any) => v.value).join(','),
        })
    }

    const confirmDeleteService = () => {
        setOpenConfirmDeleteDialog(true)
    }

    const [
        deleteTripScheduleService,
        { loading: deleteTripScheduleServiceLoading },
    ] = useMutation(DeleteTripScheduleServices, {
        onCompleted: (response: any) => {
            if (response.deleteTripScheduleServices) {
                setOpenConfirmDeleteDialog(false) // Close the dialog first
                toast({
                    title: 'Success',
                    description: 'Trip Schedule Service deleted successfully',
                })
                router.push('/trip-schedule-services')
            } else {
                setOpenConfirmDeleteDialog(false) // Close the dialog first
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Error deleting Trip Schedule Service',
                })
            }
        },
        onError: (error: any) => {
            setOpenConfirmDeleteDialog(false) // Close the dialog first
            toast({
                variant: 'destructive',
                title: 'Error',
                description: `Error: ${error.message}`,
            })
            console.error('deleteTripScheduleService onError', error.message)
        },
    })

    const handleDeleteService = async () => {
        await deleteTripScheduleService({
            variables: {
                ids: [id],
            },
        })
    }
    const handleTripReportScheduleChange = (e: any) => {
        setSelectedTripReportSchedules(e)
        setTss({
            ...tss,
            tripReportSchedules: e.map((v: any) => v.value).join(','),
        })
    }
    const [
        readOneTripScheduleService,
        { loading: readOneTripScheduleServiceLoading },
    ] = useLazyQuery(ReadOneTripScheduleService, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneTripScheduleService
            if (data) {
                const tripReportSchedulesFromData =
                    data.tripReportSchedules.nodes
                const tripReportScheduleList = tripReportSchedulesFromData.map(
                    (item: any) => ({
                        label: `${item.title} @ ${item.departTime}`,
                        value: item.id,
                    }),
                )
                setTripReportSchedules(tripReportScheduleList)
                const vehicles = data.vehicles.nodes.map((v: any) => v.id)
                const trs = tripReportSchedulesFromData.map((v: any) => v.id)
                setTss({
                    ...data,
                    vehicles: vehicles.join(','),
                    tripReportSchedules: trs.join(','),
                    __typename: undefined,
                })
            }
        },
        onError: (error: any) => {
            console.error('readOneTripScheduleService error', error)
        },
    })
    const loadTripScheduleService = async () => {
        await readOneTripScheduleService({ variables: { id: id } })
    }
    const handleSetVessels = (e: any) => {
        // Filter vessel types
        const allowedVesselTypes = [
            'SLALL',
            'Tug_Boat',
            'Passenger_Ferry',
            'Water_Taxi',
        ]
        const vesselList = e
            .filter((vessel: any) =>
                allowedVesselTypes.includes(vessel.vesselType),
            )
            .map((vessel: any) => ({
                label: vessel.title,
                value: vessel.id,
            }))
        setVessels(vesselList)
    }
    const { toast } = useToast()

    const validateInputs = () => {
        let errorMessage = ''
        if (isEmpty(trim(tss.title))) {
            errorMessage += '\nThe title is required.'
        }
        if (isEmpty(selectedVessels)) {
            errorMessage += '\nAt least one vessel is required.'
        }

        if (!isEmpty(trim(errorMessage))) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: trim(errorMessage),
            })
            return false
        } else {
            return true
        }
    }
    const [
        updateTripScheduleService,
        { loading: updateTripScheduleServiceLoading },
    ] = useMutation(UpdateTripScheduleService, {
        onCompleted: (_response: any) => {
            router.push('/trip-schedule-services')
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('updateTripScheduleService onError', error.message)
        },
    })
    const saveTSS = async () => {
        const validated = validateInputs()
        if (!validated) return
        if (+id > 0) {
            // update
            await updateTripScheduleService({
                variables: { input: tss },
            })
        }
    }
    getVesselList(handleSetVessels)
    useEffect(() => {
        if (+id > 0) {
            loadTripScheduleService()
        }
    }, [id])
    useEffect(() => {
        if (!isEmpty(vessels) && !isEmpty(tss.vehicles)) {
            const sv = tss.vehicles
                .split(',')
                .map((v: any) =>
                    vessels.find((vessel: any) => +vessel.value === +v),
                )
            setSelectedVessels(sv)
        }
    }, [vessels, tss])
    useEffect(() => {
        if (
            !isEmpty(tripReportSchedules) &&
            !isEmpty(tss.tripReportSchedules)
        ) {
            const sv = tss.tripReportSchedules
                .split(',')
                .map((item: any) =>
                    tripReportSchedules.find(
                        (trs: any) => +trs.value === +item,
                    ),
                )
            setSelectedTripReportSchedules(sv)
        }
    }, [tripReportSchedules, tss])
    return (
        <div className="w-full mb-20 md:mb-0">
            <div className="px-2 lg:px-4 mt-2 ">
                <div className="flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start">
                    <H2>{+id > 0 ? 'Edit' : 'New'} Trip Schedule Service</H2>
                </div>
                <Separator className="my-4" />
                <div className="my-4">
                    <Label>Title</Label>
                    <Input
                        defaultValue={tss.title}
                        onChange={handleOnChangeTitle}
                        type="text"
                        placeholder="Title"
                    />
                </div>
                <div className="my-4">
                    <Label>Internal Code</Label>
                    <Input
                        defaultValue={tss.code}
                        onChange={handleOnChangeCode}
                        type="text"
                        placeholder="Internal code"
                    />
                </div>
                <div className="flex items-center gap-2 my-4">
                    <Checkbox
                        id="share-pax-data"
                        checked={tss.sharePaxData}
                        onCheckedChange={handleSetSharePaxData}
                    />
                    <Label
                        htmlFor="share-pax-data"
                        className="font-medium cursor-pointer">
                        Share Trip Data (Passenger Numbers)
                    </Label>
                </div>
                <div className="my-4">
                    <Label>Transit ID</Label>
                    <Input
                        defaultValue={tss.transitID}
                        onChange={handleOnChangeTransitID}
                        type="text"
                        placeholder="Transit ID"
                    />
                </div>
                <div className="my-4">
                    <Label>Public Transit Description</Label>
                    <Input
                        defaultValue={tss.publicDescription}
                        onChange={handleOnChangePublicDescription}
                        type="text"
                        placeholder="Public Transit Description"
                    />
                </div>
                <div className="my-4">
                    <Label>Vessels</Label>
                    <Combobox
                        options={vessels}
                        value={selectedVessels}
                        onChange={handleVesselChange}
                        placeholder="Select Vessels"
                        multi={true}
                    />
                </div>
                <div className="my-4">
                    <Label>Trip Schedules</Label>
                    <Combobox
                        options={tripReportSchedules}
                        value={selectedTripReportSchedules}
                        onChange={handleTripReportScheduleChange}
                        placeholder="Select Trip Schedule"
                        multi={true}
                    />
                </div>
                <div className="flex justify-end gap-2">
                    <Button
                        variant="text"
                        iconLeft={X}
                        onClick={() => {
                            router.push('/trip-schedule-services')
                        }}>
                        Cancel
                    </Button>
                    {+id > 0 && (
                        <Button
                            variant="destructive"
                            iconLeft={Trash2}
                            onClick={confirmDeleteService}>
                            Delete
                        </Button>
                    )}
                    <Button
                        iconLeft={Save}
                        onClick={saveTSS}
                        isLoading={
                            readOneTripScheduleServiceLoading ||
                            updateTripScheduleServiceLoading
                        }>
                        {+id > 0 ? 'Update' : 'Save'} Changes
                    </Button>
                </div>
            </div>
            <AlertDialogNew
                openDialog={openConfirmDeleteDialog}
                setOpenDialog={setOpenConfirmDeleteDialog}
                handleCreate={handleDeleteService}
                title="Delete Trip Schedule Service"
                description="Are you sure you want to delete this Trip Schedule Service?"
                actionText="Delete"
                variant="danger"
            />
        </div>
    )
}

export default TripScheduleServiceForm
