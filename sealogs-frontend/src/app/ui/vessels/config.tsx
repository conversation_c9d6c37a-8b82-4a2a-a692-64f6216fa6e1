'use client'

import {
    Confi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    PageHeader,
} from './components/logbook-config'

interface IProps {
    logBookID: number
    vesselID: number
}

export default function LogbookConfig({ logBookID, vesselID }: IProps) {
    return (
        <ConfigFormProvider vesselID={vesselID} logBookID={logBookID}>
            <ContentWrapper />
        </ConfigFormProvider>
    )
}

function ContentWrapper() {
    return (
        <div className="space-y-6">
            <PageHeader />
            <MainContent />
            <PageFooter />
        </div>
    )
}
