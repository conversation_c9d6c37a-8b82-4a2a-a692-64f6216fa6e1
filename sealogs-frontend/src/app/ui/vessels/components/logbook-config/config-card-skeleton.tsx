import { Card, CardContent, CardHeader } from '@/components/ui'
import { Skeleton } from '@/components/ui/skeleton'

export function ConfigCardSkeleton() {
    return (
        <Card>
            <CardHeader>
                {/* Section Title */}
                <Skeleton className="h-6 w-40 mb-4" />
            </CardHeader>

            <CardContent>
                {/* Fake rows */}
                {Array.from({ length: 4 }).map((_, i) => (
                    <div
                        key={i}
                        className="flex items-center justify-between py-3 border-b last:border-b-0">
                        {/* Radio buttons */}
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <Skeleton className="h-4 w-4 rounded-full" />
                        </div>

                        {/* Label placeholder */}
                        <Skeleton className="h-4 w-48 flex-1 mx-4" />

                        {/* Edit icon placeholder */}
                        <Skeleton className="h-4 w-4" />
                    </div>
                ))}

                {Array.from({ length: 4 }).map((_, i) => (
                    <div
                        key={i}
                        className="flex items-center justify-between py-3 border-b last:border-b-0">
                        {/* Radio buttons */}
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <Skeleton className="h-4 w-4 rounded-full" />
                        </div>

                        {/* Label placeholder */}
                        <Skeleton className="h-4 w-48 flex-1 mx-4" />

                        {/* Edit icon placeholder */}
                        <Skeleton className="h-4 w-4" />
                    </div>
                ))}

                {/* Buttons */}
                <div className="flex gap-2 mt-4">
                    <Skeleton className="h-8 w-24 rounded" />
                    <Skeleton className="h-8 w-28 rounded" />
                </div>

                <div className="h-20"></div>

                {/* Fake rows */}
                {Array.from({ length: 4 }).map((_, i) => (
                    <div
                        key={i}
                        className="flex items-center justify-between py-3 border-b last:border-b-0">
                        {/* Radio buttons */}
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <Skeleton className="h-4 w-4 rounded-full" />
                        </div>

                        {/* Label placeholder */}
                        <Skeleton className="h-4 w-48 flex-1 mx-4" />

                        {/* Edit icon placeholder */}
                        <Skeleton className="h-4 w-4" />
                    </div>
                ))}

                {Array.from({ length: 4 }).map((_, i) => (
                    <div
                        key={i}
                        className="flex items-center justify-between py-3 border-b last:border-b-0">
                        {/* Radio buttons */}
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <Skeleton className="h-4 w-4 rounded-full" />
                        </div>

                        {/* Label placeholder */}
                        <Skeleton className="h-4 w-48 flex-1 mx-4" />

                        {/* Edit icon placeholder */}
                        <Skeleton className="h-4 w-4" />
                    </div>
                ))}

                {/* Buttons */}
                <div className="flex gap-2 mt-4">
                    <Skeleton className="h-8 w-24 rounded" />
                    <Skeleton className="h-8 w-28 rounded" />
                </div>
            </CardContent>
        </Card>
    )
}
