'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui'
import React, { useMemo } from 'react'
import {
    fieldIsGroup,
    filterFieldClasses,
    getCategory,
    getFieldName,
    getFieldsNotInConfig,
    getGroupFields,
    getLevelThreeCategory,
    getLevelThreeCategoryGroup,
    getNoCheckedStatus,
    getTabTitle,
    getYesCheckedStatus,
    isCategorised,
    isFieldInConfig,
    isFileField,
    isInLevel,
    sortCustomisedComponentFields,
    subCategoryVisibilityCheck,
} from '../../actions'
import { DocumentField } from './document-field'
import { BanIcon, CheckCircle, RotateCcw } from 'lucide-react'
import { cn } from '@/app/lib/utils'
import {
    CheckFieldContent,
    CheckFieldTopContent,
} from '@/components/daily-check-field'
import { ConfigCheckField } from './config-check-field'
import {
    Required<PERSON><PERSON><PERSON>,
    SLALL_LogBookFields,
} from '@/app/lib/logbook-configuration'
import { useToast } from '@/hooks/use-toast'
import vesselTypes from '@/app/lib/vesselTypes'
import { GroupFieldHeaderCheck } from './group-field-header-check'
import { useConfigForm } from './config-form-context'
import { ResetButton } from './reset-button'
import { useMutation } from '@apollo/client'
import { CREATE_CUSTOMISED_COMPONENT_FIELD } from '@/app/lib/graphQL/mutation'

export function LogBookConfigTabContent({ tabValue }: { tabValue: string }) {
    const { toast } = useToast()
    const {
        slallFields,
        vessel,
        logBookConfig,
        documents,
        setDocuments,
        imCrew,
        customisedLogBookComponents,
        dailyCheckCategories,
        dailyCheckCategory,
        setDailyCheckCategory,
        levelThreeCategory,
        setLevelThreeCategory,
        levelThreeCategories,
        updatedFields,
        setUpdatedFields,
        updatedLocalFields,
        setUpdatedLocalFields,
        editConfigDialog,
        descriptionDialog,
        updateCustomisedComponentField,
        loadLogBookConfig,
        activateCustomisedComponent,
        deactivateCustomisedComponent,
        updateCustomisedComponent,
    } = useConfigForm()

    //start apollo query mutation
    const [createCustomisedComponentField] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponentField
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )
    //end apollo query mutation

    //start memoization hook
    const logBookComponents = useMemo(() => {
        return customisedLogBookComponents.filter((component: any) => {
            return (
                tabValue.replace('Logbook', 'LogBook') ===
                    component.title.replace('Logbook', 'LogBook') ||
                (tabValue === 'Crew Members' &&
                    component.title === 'Crew Welfare') ||
                (tabValue === 'Engine Reports' &&
                    (component.componentClass === 'Engineer_LogBookComponent' ||
                        component.componentClass === 'Fuel_LogBookComponent'))
            )
        })
    }, [customisedLogBookComponents, tabValue])

    //end memoization hook

    //start method
    const getFilteredAndSortedCustomisedComponentFields = (component: any) => {
        const fields = component.customisedComponentFields.nodes
            .filter(
                (customFields: any, index: number, self: any[]) =>
                    self.findIndex(
                        (c: any) => c.fieldName === customFields.fieldName,
                    ) === index,
            )
            .filter((field: any) =>
                customisedComponentFieldsCombinedFilter(field),
            )
            .sort(sortCustomisedComponentFields)

        return fields
    }

    const updateFieldStatus = (field: any, status: string) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const appendedData = [
                ...updatedFields.filter(
                    (updatedField: any) => updatedField.fieldID !== field.id,
                ),
                { fieldID: field.id, status: status },
            ]
            setUpdatedFields(appendedData)
            document.body.style.cursor = 'wait'
            updateCustomisedComponentField({
                variables: {
                    input: {
                        id: field.id,
                        status: status,
                    },
                },
            })
        } else {
            setUpdatedLocalFields([
                ...updatedLocalFields.filter(
                    (updatedField: any) =>
                        updatedField.localID !== field.localID,
                ),
                { ...field, status: status },
            ])
            document.body.style.cursor = 'wait'
            createCustomisedComponentField({
                variables: {
                    input: {
                        customisedFieldTitle: field?.title
                            ? field.title
                            : field.label,
                        customisedLogBookComponentID:
                            field.customisedLogBookComponentID,
                        fieldName: field.value,
                        status: status,
                        sortOrder: field.sortOrder || 0,
                    },
                },
            })
        }
    }

    const isRequiredField = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const isRequired = RequiredFields.includes(field.fieldName)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                updateFieldStatus(field, 'Required')
            }
            return isRequired
        } else {
            const isRequired = RequiredFields.includes(field.value)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                if (
                    updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ).length === 0
                ) {
                    document.body.style.cursor = 'wait'
                    createCustomisedComponentField({
                        variables: {
                            input: {
                                customisedFieldTitle: field?.title
                                    ? field.title
                                    : field.label,
                                customisedLogBookComponentID:
                                    field.customisedLogBookComponentID,
                                fieldName: field.value,
                                status: 'Required',
                                sortOrder: field.sortOrder || 0,
                            },
                        },
                    })
                }
                setUpdatedLocalFields([
                    ...updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ),
                    { ...field, status: 'Required' },
                ])
            }
            return isRequired
        }
    }

    const checkLevelThree = (field: any): boolean => {
        if (
            getLevelThreeCategory(
                field,
                slallFields,
                tabValue.replace('Logbook', 'LogBook'),
            )
        ) {
            if (
                getLevelThreeCategory(
                    field,
                    slallFields,
                    tabValue.replace('Logbook', 'LogBook'),
                ) === levelThreeCategory
            ) {
                return true
            }
            return false
        }
        return true
    }

    const openEditConfigDialog = (field: any) => {
        editConfigDialog.openDialog({
            field,
            title: field?.customisedFieldTitle
                ? field?.customisedFieldTitle
                : getFieldName(field, slallFields),
            isFieldGroup: fieldIsGroup(
                field,
                slallFields,
                tabValue.replace('Logbook', 'LogBook'),
            ),
        })
    }

    const filterFieldsWithGroup = (fields: any, componentClass: string) => {
        const logbookFields = slallFields
        const defaultConfig = logbookFields.map((component: any) => component)
        var groupFields: any = []
        var groups: any = []
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tabValue.replace('Logbook', 'LogBook') ===
                    defaultLogBookComponents.label.replace(
                        'Logbook',
                        'LogBook',
                    ) &&
                defaultLogBookComponents.componentClass === componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    if (defaultField.groupTo && defaultField.level !== 3) {
                        if (!groups.includes(defaultField.groupTo)) {
                            groups.push(defaultField.groupTo)
                        }
                    }
                })
            }
        })
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tabValue.replace('Logbook', 'LogBook') ===
                defaultLogBookComponents.label.replace('Logbook', 'LogBook')
            ) {
                defaultLogBookComponents.items.forEach(
                    (defaultField: any, index: number) => {
                        groups.forEach((group: any) => {
                            if (
                                defaultField.value === group &&
                                !isFieldInConfig(
                                    defaultField.value,
                                    logBookConfig,
                                    tabValue,
                                )
                            ) {
                                if (defaultField.level !== 3) {
                                    groupFields.push({
                                        ...defaultField,
                                        description: null,
                                        fieldName: defaultField.value,
                                        id: index + '0',
                                        sortOrder: index,
                                        status: defaultField.status,
                                    })
                                }
                            }
                        })
                    },
                )
            }
        })
        if (groups.length > 0) {
            const flatGroupFields = groupFields.flatMap(
                (group: any) => group.value,
            )
            return [
                ...groupFields.map((group: any) => {
                    let groupData = group
                    fields.map((field: any) => {
                        if (group.value === field.fieldName) {
                            groupData = { ...group, ...field }
                        }
                    })
                    return groupData
                }),
                ...fields.filter(
                    (field: any) => !flatGroupFields.includes(field.fieldName),
                ),
            ]
                .filter((field: any) =>
                    filterFieldsWithGroupCombinedFilter(field),
                )
                .sort(sortCustomisedComponentFields)
        }
        return fields
            .filter((field: any) => filterFieldsWithGroupCombinedFilter(field))
            .sort(sortCustomisedComponentFields)
    }

    const filterFieldsWithGroupCombinedFilter = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tabValue.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tabValue === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tabValue.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tabValue === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        }
    }

    const checkLevelThreeGroup = (field: any) => {
        const levelThreeCategoryGroup = getLevelThreeCategoryGroup(
            field,
            slallFields,
            tabValue.replace('Logbook', 'LogBook'),
        )
        if (levelThreeCategoryGroup) {
            if (levelThreeCategoryGroup === levelThreeCategory) {
                return true
            }
            return false
        }
        return true
    }

    const customisedComponentFieldsCombinedFilter = (field: any): boolean => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tabValue.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tabValue === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tabValue.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tabValue === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        }
    }

    const activateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields + '||' + category,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const deactivateCustomisedSubComponent = (
        component: any,
        category: string,
    ) => {
        if (component.subFields) {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: component.subFields
                            .split('||')
                            .filter((field: any) => field !== category)
                            .join('||'),
                    },
                },
            })
        } else {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: dailyCheckCategories
                            .filter((cat: any) => cat != category)
                            .join('||'),
                    },
                },
            })
        }
    }

    const activateCustomisedLevelThreeComponent = (
        component: any,
        levelThree: any,
    ) => {
        if (component.subFields) {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields:
                            component.subFields + '||' + levelThree.label,
                    },
                },
            })
        } else {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: dailyCheckCategories
                            .concat(levelThree.label)
                            .join('||'),
                    },
                },
            })
        }
        //if (levelThree?.status === 'disabled') {
        levelThreeCategories.map((category: any) => {
            if (category.label === levelThree.label) {
                category.status = 'Enabled'
            }
            return category
        })
        //}
    }

    const deactivateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter(
                                    (field: any) => field !== levelThree.label,
                                )
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'enabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Disabled'
                }
                return category
            })
            //}
        }

    //end method

    return (
        <div className="border rounded-lg grid grid-cols-3 border-border">
            {logBookComponents.map((component: any) => (
                <React.Fragment key={component.id}>
                    <div className="flex bg-background border-b border-border flex-row md:flex-col items-center md:items-start justify-between gap-4 col-span-3 md:col-span-1 p-4">
                        <div className="text-xl font-medium flex items-center gap-2">
                            {getTabTitle(component, SLALL_LogBookFields)}{' '}
                            {component.active ? (
                                ''
                            ) : (
                                <BanIcon className="text-destructive" />
                            )}
                        </div>
                        {!isCategorised(
                            component,
                            slallFields,
                            logBookConfig,
                        ) &&
                            !imCrew && (
                                <div className="flex gap-2">
                                    {component.active ? (
                                        <>
                                            <Button
                                                onClick={() =>
                                                    deactivateCustomisedComponent(
                                                        component.id,
                                                    )
                                                }
                                                iconLeft={BanIcon}
                                                variant={'destructive'}>
                                                Disable
                                            </Button>

                                            <ResetButton
                                                component={component}
                                            />
                                        </>
                                    ) : (
                                        <Button
                                            variant="primary"
                                            iconLeft={CheckCircle}
                                            onClick={() =>
                                                activateCustomisedComponent(
                                                    component.id,
                                                )
                                            }>
                                            Enable
                                        </Button>
                                    )}
                                </div>
                            )}
                    </div>
                    {isCategorised(component, slallFields, logBookConfig) && (
                        <>
                            <div className="md:col-span-2 hidden md:block bg-background border-b border-border">
                                {/* Categorised */}
                            </div>
                            {/* remove later */}
                            <div className="col-span-3 p-4 overflow-x-auto">
                                {dailyCheckCategories && (
                                    <Tabs
                                        value={dailyCheckCategory}
                                        onValueChange={(newTab) => {
                                            setDailyCheckCategory(newTab)
                                        }}>
                                        <TabsList>
                                            {dailyCheckCategories.map(
                                                (category: any) => (
                                                    <TabsTrigger
                                                        key={category}
                                                        value={category}>
                                                        {category !==
                                                        'Engine Checks'
                                                            ? category
                                                            : 'Engine, steering, electrical & alt power'}
                                                    </TabsTrigger>
                                                ),
                                            )}
                                        </TabsList>
                                    </Tabs>
                                )}
                            </div>
                            {levelThreeCategories &&
                                isInLevel(
                                    dailyCheckCategory,
                                    3,
                                    slallFields,
                                ) && (
                                    <div className={cn('col-span-3 p-4')}>
                                        <Tabs
                                            value={levelThreeCategory}
                                            onValueChange={(newValue) =>
                                                setLevelThreeCategory(newValue)
                                            }>
                                            <TabsList>
                                                {levelThreeCategories.map(
                                                    (category: any) => (
                                                        <TabsTrigger
                                                            key={category.label}
                                                            value={
                                                                category.label
                                                            }>
                                                            {category.label}
                                                        </TabsTrigger>
                                                    ),
                                                )}
                                            </TabsList>
                                        </Tabs>
                                    </div>
                                )}
                            {dailyCheckCategories &&
                                dailyCheckCategories
                                    .filter(
                                        (category) =>
                                            category === dailyCheckCategory,
                                    )
                                    .map((category: any, index: number) => (
                                        <div
                                            key={index}
                                            className={cn(
                                                'flex border-b border-border border-r border-t justify-between bg-background',
                                                isInLevel(
                                                    dailyCheckCategory,
                                                    3,
                                                    slallFields,
                                                )
                                                    ? 'col-span-3 flex-row items-center'
                                                    : 'col-span-3 md:col-span-1 flex-row md:flex-col items-center md:items-start',
                                                // dailyCheckCategory !==
                                                //     category && 'hidden',
                                            )}>
                                            <div className="p-4 font-medium text-xl">
                                                {category !== 'Engine Checks'
                                                    ? category
                                                    : 'Engine, steering, electrical & alt power'}{' '}
                                                {component.active ? (
                                                    ''
                                                ) : (
                                                    <BanIcon className="text-destructive" />
                                                )}
                                            </div>
                                            {category !== 'Engine Checks' ? (
                                                <div className="mt-2 p-4">
                                                    {(component.subFields ==
                                                        null ||
                                                        (component?.subFields &&
                                                            component.subFields
                                                                .split('||')
                                                                .includes(
                                                                    category,
                                                                ))) &&
                                                    !imCrew ? (
                                                        <>
                                                            <Button
                                                                onClick={() =>
                                                                    deactivateCustomisedSubComponent(
                                                                        component,
                                                                        category,
                                                                    )
                                                                }
                                                                iconLeft={
                                                                    BanIcon
                                                                }
                                                                variant={
                                                                    'destructive'
                                                                }>
                                                                Disable
                                                            </Button>
                                                        </>
                                                    ) : (
                                                        <>
                                                            {!imCrew && (
                                                                <>
                                                                    <Button
                                                                        onClick={() =>
                                                                            activateCustomisedSubComponent(
                                                                                component,
                                                                                category,
                                                                            )
                                                                        }
                                                                        iconLeft={
                                                                            CheckCircle
                                                                        }
                                                                        variant={
                                                                            'primary'
                                                                        }>
                                                                        Enable
                                                                    </Button>
                                                                </>
                                                            )}
                                                        </>
                                                    )}
                                                </div>
                                            ) : (
                                                <div className="flex items-center pr-4">
                                                    {/* Level Three Categories Enable/Disable Buttons */}
                                                    {levelThreeCategories &&
                                                        levelThreeCategories.map(
                                                            (levelThree: any) =>
                                                                levelThree.label ===
                                                                    levelThreeCategory && (
                                                                    <div
                                                                        key={
                                                                            levelThree.label
                                                                        }>
                                                                        {(component.subFields ==
                                                                            null ||
                                                                            (component?.subFields &&
                                                                                component.subFields
                                                                                    .split(
                                                                                        '||',
                                                                                    )
                                                                                    .includes(
                                                                                        levelThree.label,
                                                                                    ))) &&
                                                                        !imCrew ? (
                                                                            <>
                                                                                <Button
                                                                                    onClick={() =>
                                                                                        deactivateCustomisedLevelThreeComponent(
                                                                                            component,
                                                                                            levelThree,
                                                                                        )
                                                                                    }
                                                                                    iconLeft={
                                                                                        BanIcon
                                                                                    }
                                                                                    variant={
                                                                                        'destructive'
                                                                                    }>
                                                                                    Disable
                                                                                </Button>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                {!imCrew && (
                                                                                    <>
                                                                                        <Button
                                                                                            onClick={() =>
                                                                                                activateCustomisedLevelThreeComponent(
                                                                                                    component,
                                                                                                    levelThree,
                                                                                                )
                                                                                            }
                                                                                            iconLeft={
                                                                                                CheckCircle
                                                                                            }
                                                                                            variant={
                                                                                                'primary'
                                                                                            }>
                                                                                            Enable
                                                                                        </Button>
                                                                                    </>
                                                                                )}
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                ),
                                                        )}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                        </>
                    )}

                    {(tabValue === 'Pre-Departure Checks' &&
                        isInLevel(dailyCheckCategory, 3, slallFields)) ===
                        false && (
                        <div
                            className={cn(
                                'col-span-3 md:col-span-2 border-b border-t border-border',
                            )}>
                            <CheckFieldTopContent className="justify-start" />
                            {getFilteredAndSortedCustomisedComponentFields(
                                component,
                            ).map((field: any, index: any) => (
                                <div
                                    key={field.id}
                                    className={`${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tabValue) && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                    <CheckFieldContent>
                                        <ConfigCheckField
                                            title={
                                                field?.customisedFieldTitle
                                                    ? field.customisedFieldTitle
                                                    : getFieldName(
                                                          field,
                                                          slallFields,
                                                      )
                                            }
                                            field={field}
                                            isRequired={isRequiredField(field)}
                                            className={cn(
                                                subCategoryVisibilityCheck(
                                                    component,
                                                    tabValue.replace(
                                                        'Logbook',
                                                        'LogBook',
                                                    ),
                                                    dailyCheckCategory,
                                                    levelThreeCategories,
                                                    levelThreeCategory,
                                                ),
                                                !component.active &&
                                                    'pointer-events-none opacity-50',
                                                `field-${getCategory(field, slallFields, tabValue)}`,
                                            )}
                                            isDisabled={
                                                imCrew || isRequiredField(field)
                                            }
                                            value={
                                                !getNoCheckedStatus(
                                                    field,
                                                    updatedFields,
                                                    updatedLocalFields,
                                                )
                                                    ? 'yes'
                                                    : 'no'
                                            }
                                            onUpdateField={(field, status) =>
                                                updateFieldStatus(field, status)
                                            }
                                            onDescriptionClick={(
                                                title,
                                                description,
                                            ) =>
                                                descriptionDialog.openDialog(
                                                    title,
                                                    description,
                                                )
                                            }
                                            onCustomizeClick={(field) =>
                                                openEditConfigDialog(field)
                                            }
                                        />
                                        {field?.fieldType === 'files' ||
                                            (isFileField(
                                                field,
                                                slallFields,
                                                tabValue.replace(
                                                    'Logbook',
                                                    'LogBook',
                                                ),
                                            ) &&
                                                (!isCategorised(
                                                    component,
                                                    slallFields,
                                                    logBookConfig,
                                                ) ||
                                                    dailyCheckCategory ===
                                                        field.fieldSet) && (
                                                    <div
                                                        className={cn(
                                                            'px-4',
                                                            !component.active &&
                                                                'pointer-events-none opacity-50',
                                                            getCategory(
                                                                field,
                                                                slallFields,
                                                                tabValue.replace(
                                                                    'Logbook',
                                                                    'LogBook',
                                                                ),
                                                            ),
                                                            // isCategorised(
                                                            //     component,
                                                            //     slallFields,
                                                            //     logBookConfig,
                                                            // ) &&
                                                            //     dailyCheckCategory !==
                                                            //         field.fieldSet &&
                                                            //     'hidden',
                                                        )}>
                                                        <div></div>
                                                        <div className="colspan-3">
                                                            <DocumentField
                                                                documents={
                                                                    documents
                                                                }
                                                                setDocuments={
                                                                    setDocuments
                                                                }
                                                                fileUploadText="Policies"
                                                            />
                                                        </div>
                                                    </div>
                                                ))}
                                    </CheckFieldContent>
                                </div>
                            ))}
                            {getFieldsNotInConfig(
                                component,
                                slallFields,
                                logBookConfig,
                            )
                                .filter((field: any) =>
                                    customisedComponentFieldsCombinedFilter(
                                        field,
                                    ),
                                )
                                .map((field: any, index: number) => (
                                    <div
                                        key={field.value + '_' + index}
                                        className={`${filterFieldClasses(field, slallFields, tabValue.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === (field?.fieldSet ? field.fieldSet : 'Other') && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                        {/* NOT IN CONFIG CHECK FIELD */}
                                        <ConfigCheckField
                                            title={getFieldName(
                                                field,
                                                slallFields,
                                            )}
                                            field={field}
                                            isRequired={isRequiredField({
                                                ...field,
                                                localID: index,
                                                customisedLogBookComponentID:
                                                    component.id,
                                                status: 'Required',
                                            })}
                                            isDisabled={
                                                imCrew ||
                                                isRequiredField({
                                                    ...field,
                                                    localID: index,
                                                    customisedLogBookComponentID:
                                                        component.id,
                                                    status: 'Required',
                                                })
                                            }
                                            value={
                                                undefined //default value for not in config field is undefined
                                            }
                                            onUpdateField={(field, status) =>
                                                updateFieldStatus(
                                                    {
                                                        ...field,
                                                        customisedLogBookComponentID:
                                                            component.id,
                                                    },
                                                    status,
                                                )
                                            }
                                            className={`${subCategoryVisibilityCheck(component, tabValue.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tabValue.replace('Logbook', 'LogBook'))}`}
                                            onCustomizeClick={() => {
                                                toast({
                                                    title: 'Warning!',
                                                    description:
                                                        'You can rename this field after setting its value to either "Yes" or "No."',
                                                    variant: 'destructive',
                                                })
                                            }}
                                        />

                                        {field?.fieldType === 'files' && (
                                            <div
                                                className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tabValue.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                <div></div>
                                                <div className="colspan-3">
                                                    <DocumentField
                                                        documents={documents}
                                                        setDocuments={
                                                            setDocuments
                                                        }
                                                        fileUploadText="Policies"
                                                        showFileUpload={!imCrew}
                                                    />
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                        </div>
                    )}

                    {/* Group Component Fields */}
                    <div className="col-span-3">
                        {filterFieldsWithGroup(
                            component.customisedComponentFields.nodes.filter(
                                (
                                    customFields: any,
                                    index: number,
                                    self: any[],
                                ) =>
                                    self.findIndex(
                                        (c: any) =>
                                            c.fieldName ===
                                            customFields.fieldName,
                                    ) === index,
                            ),
                            component.componentClass,
                        ).map((field: any, index: number) => (
                            <div
                                key={field.id}
                                className={`${subCategoryVisibilityCheck(component, tabValue.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tabValue.replace('Logbook', 'LogBook')) && checkLevelThreeGroup(field) ? '' : 'hidden') : ''} border rounded-lg m-4 border-border`}>
                                <div
                                    className={`${component.active ? '' : 'pointer-events-none opacity-50'} field-${getCategory(field, slallFields, tabValue.replace('Logbook', 'LogBook'))}`}>
                                    <GroupFieldHeaderCheck
                                        className="border-b border-border p-3 bg-background"
                                        title={
                                            field?.customisedFieldTitle
                                                ? field.customisedFieldTitle
                                                : getFieldName(
                                                      field,
                                                      slallFields,
                                                  )
                                        }
                                        value={
                                            field?.id > 0
                                                ? !getNoCheckedStatus(
                                                      {
                                                          ...field,
                                                          localID: index,
                                                          customisedLogBookComponentID:
                                                              component.id,
                                                      },
                                                      updatedFields,
                                                      updatedLocalFields,
                                                  )
                                                    ? 'yes'
                                                    : 'no'
                                                : undefined
                                        }
                                        field={field}
                                        isDisabled={imCrew}
                                        onUpdateField={(field, status) => {
                                            updateFieldStatus(
                                                {
                                                    ...field,
                                                    customisedLogBookComponentID:
                                                        component.id,
                                                },
                                                status,
                                            )
                                        }}
                                        onCustomizeClick={(field) => {
                                            if (field?.id > 0) {
                                                openEditConfigDialog(field)
                                            } else {
                                                toast({
                                                    title: 'Warning!',
                                                    description:
                                                        'You can rename this group after setting its value to either "Yes" or "No."',
                                                    variant: 'destructive',
                                                })
                                            }
                                        }}
                                        onDescriptionClick={(
                                            title,
                                            description,
                                        ) =>
                                            descriptionDialog.openDialog(
                                                title,
                                                description,
                                            )
                                        }
                                    />
                                    <div className="">
                                        <CheckFieldTopContent className="justify-start" />
                                        <div
                                            className={`md:col-span-2 ${
                                                getNoCheckedStatus(
                                                    {
                                                        ...field,
                                                        localID: index,
                                                        customisedLogBookComponentID:
                                                            component.id,
                                                    },
                                                    updatedFields,
                                                    updatedLocalFields,
                                                )
                                                    ? 'pointer-events-none opacity-50'
                                                    : ''
                                            }`}>
                                            {getGroupFields(
                                                field.fieldName,
                                                slallFields,
                                                tabValue.replace(
                                                    'Logbook',
                                                    'LogBook',
                                                ),
                                                logBookConfig,
                                            ).map(
                                                (
                                                    groupField: any,
                                                    index: number,
                                                ) => (
                                                    <ConfigCheckField
                                                        key={
                                                            groupField.value +
                                                            '_' +
                                                            index
                                                        }
                                                        title={
                                                            groupField?.customisedFieldTitle
                                                                ? groupField.customisedFieldTitle
                                                                : getFieldName(
                                                                      groupField,
                                                                      slallFields,
                                                                  )
                                                        }
                                                        isRequired={isRequiredField(
                                                            groupField,
                                                        )}
                                                        field={groupField}
                                                        className={`${groupField?.id} ${component.active ? '' : 'pointer-events-none opacity-50 77'} ${getCategory(groupField, slallFields, tabValue.replace('Logbook', 'LogBook'))}`}
                                                        isDisabled={
                                                            isRequiredField(
                                                                groupField,
                                                            ) || imCrew
                                                        }
                                                        value={
                                                            groupField?.id > 0
                                                                ? !getNoCheckedStatus(
                                                                      groupField,
                                                                      updatedFields,
                                                                      updatedLocalFields,
                                                                  )
                                                                    ? 'yes'
                                                                    : 'no'
                                                                : undefined
                                                        }
                                                        onUpdateField={(
                                                            field,
                                                            status,
                                                        ) =>
                                                            updateFieldStatus(
                                                                field,
                                                                status,
                                                            )
                                                        }
                                                        onDescriptionClick={(
                                                            title,
                                                            description,
                                                        ) =>
                                                            descriptionDialog.openDialog(
                                                                title,
                                                                description,
                                                            )
                                                        }
                                                        onCustomizeClick={(
                                                            field,
                                                        ) => {
                                                            if (field?.id > 0) {
                                                                openEditConfigDialog(
                                                                    field,
                                                                )
                                                            } else {
                                                                toast({
                                                                    description:
                                                                        'You can rename this group after setting its value to either "Yes" or "No."',
                                                                    title: 'Warning',
                                                                    variant:
                                                                        'destructive',
                                                                })
                                                            }
                                                        }}
                                                    />
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </div>
                                {field?.fieldType === 'files' ||
                                    (isFileField(
                                        field,
                                        slallFields,
                                        tabValue,
                                    ) &&
                                        (!isCategorised(
                                            component,
                                            slallFields,
                                            logBookConfig,
                                        ) ||
                                            dailyCheckCategory ===
                                                field.fieldSet) && (
                                            <div
                                                className={cn(
                                                    'px-4',
                                                    !component.active &&
                                                        'pointer-events-none opacity-50',
                                                    getCategory(
                                                        field,
                                                        slallFields,
                                                        tabValue.replace(
                                                            'Logbook',
                                                            'LogBook',
                                                        ),
                                                    ),
                                                )}>
                                                <div></div>
                                                <div className="colspan-3">
                                                    <DocumentField
                                                        documents={documents}
                                                        setDocuments={
                                                            setDocuments
                                                        }
                                                        fileUploadText="Policies"
                                                    />
                                                </div>
                                            </div>
                                        ))}
                            </div>
                        ))}
                    </div>
                </React.Fragment>
            ))}
        </div>
    )
}
