'use client'

import React from 'react'
import { useConfigForm } from './config-form-context'
import { Button } from '@/components/ui'
import { RotateCcw } from 'lucide-react'

export function ResetButton({ component }: { component: any }) {
    const {
        resetCounter,
        setResetCounter,
        slallFields,
        logBookConfig,
        updatedFields,
        updateCustomisedComponentField,
    } = useConfigForm()

    const handleClick = () => {
        const currentComponent = component

        const logbookFields = slallFields
        setResetCounter(resetCounter + 1)
        const config = logBookConfig.customisedLogBookComponents.nodes
            .filter((component: any) => component.id == currentComponent.id)
            .map((component: any) => component)

        const defaultConfig = logbookFields.map((component: any) => component)

        config.forEach((customisedLogBookComponents: any) => {
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    customisedLogBookComponents.componentClass ===
                    defaultLogBookComponents.componentClass
                ) {
                    customisedLogBookComponents.customisedComponentFields.nodes
                        .filter(
                            (customFields: any, index: number, self: any[]) =>
                                self.findIndex(
                                    (c: any) =>
                                        c.fieldName === customFields.fieldName,
                                ) === index,
                        )
                        .forEach((customFields: any) => {
                            defaultLogBookComponents.items.forEach(
                                (defaultField: any) => {
                                    if (
                                        customFields.fieldName ===
                                        defaultField.value
                                    ) {
                                        const updatedField = updatedFields.find(
                                            (updatedField: any) =>
                                                updatedField.fieldID ===
                                                customFields.id,
                                        )
                                        if (
                                            defaultField.status !=
                                            customFields.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                        if (
                                            updatedField?.fieldID &&
                                            updatedField?.status !=
                                                defaultField.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                    }
                                },
                            )
                        })
                }
            })
        })
    }

    return (
        <Button iconLeft={RotateCcw} variant={'outline'} onClick={handleClick}>
            Reset Default
        </Button>
    )
}
