import { useState } from 'react'

export interface IDescriptionDialogHook {
    title: string
    content: string
    open: boolean
    onOpenChange(open: boolean): void
    openDialog(title: string, content?: string): void
    closeDialog(): void
}

export const useDescriptionDialog = (): IDescriptionDialogHook => {
    const [open, setOpen] = useState(false)

    const [title, setTitle] = useState('')
    const [content, setContent] = useState('')

    return {
        title,
        content,
        open,
        onOpenChange(open: boolean) {
            setOpen(open)
        },
        openDialog(title: string, content?: string) {
            setTitle(title)
            setContent(content ?? '')

            setOpen(true)
        },
        closeDialog() {
            setOpen(false)
        },
    }
}
