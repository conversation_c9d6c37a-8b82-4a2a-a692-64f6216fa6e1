'use client'

import { getV<PERSON>el<PERSON>yID } from '@/app/lib/actions'
import {
    getFilteredSlallLogBookFields,
    SLALL_LogBookFields,
    type LogBookConfiguration,
} from '@/app/lib/logbook-configuration'
import {
    createContext,
    Dispatch,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from 'react'
import {
    filterByVesselType,
    getComponentsNotInConfig,
    sortCustomisedComponentFields,
    sortTabs,
} from '../../actions'
import vesselTypes from '@/app/lib/vesselTypes'
import { uniqueLogbookComponents } from '@/app/helpers/logBookHelper'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_LOGBOOK_CONFIG } from '@/app/lib/graphQL/query'
import {
    ConfigDocument,
    DailyCheckCategory,
    ILevelThreeCategory,
    ITab,
} from './types'
import {
    CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
    CreateCustomisedLogBookConfig,
    UPDATE_CUSTOMISED_COMPONENT_FIELD,
    UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
} from '@/app/lib/graphQL/mutation'
import { isCrew } from '@/app/helpers/userHelper'
import CustomisedComponentFieldModel from '@/app/offline/models/customisedComponentField'
import {
    IEditConfigDialogHook,
    useEditConfigDialog,
} from './hooks/use-edit-config'
import {
    IDescriptionDialogHook,
    useDescriptionDialog,
} from './hooks/use-description-dialog'

interface IConfigFormContext {
    //common fields
    vessel: any
    logBookConfig: any
    slallFields: LogBookConfiguration[]
    isLoading: boolean
    setIsLoading: Dispatch<boolean>
    imCrew: boolean

    //tabs related
    filteredTabs: ITab[]
    dailyCheckCategories: DailyCheckCategory[]
    levelThreeCategories: ILevelThreeCategory[]

    activeTab: string
    setActiveTab: Dispatch<string>
    dailyCheckCategory: string
    setDailyCheckCategory: Dispatch<string>
    levelThreeCategory: string
    setLevelThreeCategory: Dispatch<string>

    //state related
    resetCounter: number
    setResetCounter: Dispatch<number>
    documents: Array<ConfigDocument>
    setDocuments: Dispatch<Array<ConfigDocument>>
    updatedFields: any
    setUpdatedFields: Dispatch<any>
    updatedLocalFields: any
    setUpdatedLocalFields: Dispatch<any>

    customisedLogBookComponents: Array<any>

    editConfigDialog: IEditConfigDialogHook
    descriptionDialog: IDescriptionDialogHook

    //method related
    updateCustomisedComponentField: any
    activateCustomisedComponent: (id: number) => void
    deactivateCustomisedComponent: (id: number) => void
    createCustomisedComponent: any
    updateCustomisedComponent: any
    loadLogBookConfig: () => void
    filterComponentsNotInConfigByTab: (tab: string) => Array<any>
}

const ConfigFormContext = createContext<IConfigFormContext | undefined>(
    undefined,
)

interface IProps extends React.PropsWithChildren {
    logBookID: number
    vesselID: number
}

export const ConfigFormProvider = ({
    children,
    logBookID,
    vesselID,
}: IProps) => {
    //--- start useState
    //common state
    const [isLoading, setIsLoading] = useState(true)
    const [vessel, setVessel] = useState<any>()
    const [logBookConfig, setLogBookConfig] = useState<any>()
    const [imCrew, setImCrew] = useState<boolean>()
    const [resetCounter, setResetCounter] = useState(-1)

    //fields related state
    const [updatedFields, setUpdatedFields] = useState<any>([])
    const [updatedLocalFields, setUpdatedLocalFields] = useState<any>([])
    const [documents, setDocuments] = useState<Array<ConfigDocument>>([])
    //--- end useState

    //---start tabs related state
    const [activeTab, setActiveTab] = useState<string>('')
    const [tabs, setTabs] = useState<ITab[]>([])
    const [dailyCheckCategory, setDailyCheckCategory] = useState<string>('')
    const [dailyCheckCategories, setDailyCheckCategories] = useState<
        DailyCheckCategory[]
    >([])
    const [levelThreeCategories, setLevelThreeCategories] = useState<
        ILevelThreeCategory[]
    >([])
    const [levelThreeCategory, setLevelThreeCategory] = useState<string>('')
    //end tabs related state

    //-- start using custom hooks
    const editConfigDialog = useEditConfigDialog()
    const descriptionDialog = useDescriptionDialog()

    //-- end using custom hooks

    //---- start custom function
    const loadLogBookConfig = async () => {
        await queryLogBookConfig({
            variables: {
                id: +logBookID,
            },
        })
    }

    const handleGetLogbookConfigCompleted = (response: any) => {
        /**
         * Removes duplicate components from the customisedLogBookComponents array based on the componentClass property.
         * If a duplicate is found, it keeps the component with the highest id value.
         * @param {Object} responseData - The response data containing customisedLogBookComponents.
         * @param {Array} responseData.customisedLogBookComponents.nodes - The array of components to be deduplicated.
         * @returns {Array} The unique components with the highest id value for each componentClass.
         */
        const responseData = response.readOneCustomisedLogBookConfig
        /* const customisedLogBookComponents =
              responseData?.customisedLogBookComponents?.nodes ?? []
          let uniqueComponents = customisedLogBookComponents.reduce(
              (acc: any, current: any) => {
                  const existing = acc.find(
                      (item: any) =>
                          item.componentClass === current.componentClass,
                  )
                  if (existing) {
                      if (Number(current.id) > Number(existing.id)) {
                          return acc.map((item: any) =>
                              item.componentClass === current.componentClass
                                  ? current
                                  : item,
                          )
                      }
                      return acc
                  }
                  return [...acc, current]
              },
              [],
          )
          uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
              const matchingLogBookField = SLALL_LogBookFields.find(
                  (logBookField: any) =>
                      logBookField.componentClass ===
                      uniqueComponent.componentClass,
              )
              if (matchingLogBookField) {
                  return {
                      ...uniqueComponent,
                      title: matchingLogBookField.label,
                  }
              }
              return uniqueComponent
          }) */
        let uniqueComponents = uniqueLogbookComponents(responseData)
        uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
            if (uniqueComponent.customisedComponentFields.nodes) {
                uniqueComponent.customisedComponentFields.nodes.sort(
                    sortCustomisedComponentFields,
                )
            }
            return uniqueComponent
        })
        responseData.customisedLogBookComponents.nodes = uniqueComponents

        const data = filterByVesselType(
            responseData,
            slallFields,
            vesselTypes,
            vessel,
        )

        if (data) {
            setUpdatedLocalFields([])
            setUpdatedFields([])

            setLogBookConfig(data)

            {
                data.policies.nodes.length > 0 &&
                    setDocuments(data.policies.nodes)
            }

            if (!activeTab) {
                const tabs = data.customisedLogBookComponents?.nodes
                    .map((component: any) => ({
                        title: component.title,
                        category: component.category,
                        componentClass: component.componentClass,
                    }))
                    .sort()
                const logbookFields = slallFields
                const config = data.customisedLogBookComponents.nodes
                const defaultConfig = logbookFields.map(
                    (component: any) => component,
                )
                var componentsNotInConfig: any = []
                defaultConfig.forEach((defaultLogBookComponents: any) => {
                    var found = false
                    config.forEach((customisedLogBookComponents: any) => {
                        if (
                            customisedLogBookComponents.componentClass ===
                            defaultLogBookComponents.componentClass
                        ) {
                            found = true
                        }
                    })
                    if (!found) {
                        componentsNotInConfig.push(defaultLogBookComponents)
                    }
                })
                const additionalTabs = componentsNotInConfig.map(
                    (component: any) => ({
                        title: component.label,
                        category: component.category,
                        componentClass: component.componentClass,
                    }),
                )
                const sortedTabs = sortTabs(
                    [...tabs, ...additionalTabs],
                    slallFields,
                )

                setTabs(sortedTabs)
                setActiveTab(sortedTabs[0].title)

                // const categoryTabs: string[] = Array.from(
                //     new Set<string>(
                //         data.customisedLogBookComponents?.nodes.map(
                //             (component: any) => component.category,
                //         ),
                //     ),
                // )

                // setCategoryTabs(categoryTabs)
                // setCategoryTab(categoryTabs[0])

                // console.info('Category Tabs', categoryTabs)

                // var currentTab = false
                // sortedTabs.forEach((element: any) => {
                //     if (element.category === categoryTabs[0]) {
                //         if (!currentTab) {
                //             console.info('Set Tab 2', element.title)
                //             setTab(element.title)
                //         }
                //         currentTab = element.title
                //     }
                // })
            }
        } else {
            document.body.style.cursor = 'wait'
            createCustomisedLogBookConfig({
                variables: {
                    input: {
                        customisedLogBookID: logBookID,
                    },
                },
            })
        }
    }

    const activateCustomisedComponent = (id: number) => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: true,
                },
            },
        })
    }

    const deactivateCustomisedComponent = (id: number) => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: false,
                },
            },
        })
    }

    const handleSetDailyCheckCategories = () => {
        const logbookFields = slallFields

        const dailyCheckCategories = Array.from(
            new Set(
                logbookFields.filter((field: any) => field.subCategory).length >
                0
                    ? logbookFields
                          .filter((field: any) => field.subCategory)[0]
                          .items.filter((item: any) => item.level !== 3) // Exclude level 3 items
                          .map((item: any) => {
                              return item.fieldSet ? item.fieldSet : 'Other' // Map main categories
                          })
                    : 'Other',
            ),
        )

        setDailyCheckCategories(
            dailyCheckCategories.filter(
                (category: any) =>
                    category !== 'Checks' &&
                    category !== 'Other' &&
                    category !== 'Documentation' &&
                    category !== 'Fuel Checks',
            ),
        )
        if (!dailyCheckCategory) {
            setDailyCheckCategory(dailyCheckCategories[0])
        }
    }

    const handleLevelThreeCategories = () => {
        const logbookFields = slallFields
        const levelThreeCategories: any = Array.from(
            new Set(
                logbookFields
                    .filter((field: any) => field.subCategory)[0]
                    .items.filter((field: any) => field.level === 3)
                    .map((field: any) => {
                        return {
                            fieldSet: field.fieldSet,
                            label: field.label,
                            status: field.status,
                        }
                    }),
            ),
        )
        setLevelThreeCategories(levelThreeCategories)
        if (!levelThreeCategory) {
            setLevelThreeCategory(levelThreeCategories[0].label)
        }
    }

    //---- end custom function

    //start query and mutation
    const [queryLogBookConfig] = useLazyQuery(GET_LOGBOOK_CONFIG, {
        fetchPolicy: 'no-cache',
        onCompleted: handleGetLogbookConfigCompleted,
        onError: (error: any) => {
            console.error('queryLogBookConfig error', error)
        },
    })

    const [createCustomisedLogBookConfig] = useMutation(
        CreateCustomisedLogBookConfig,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const [updateCustomisedComponentField] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedComponentField
                deleteLocalCustomisedComponentField(data.id)
                if (resetCounter > 0) {
                    setResetCounter(resetCounter - 1)
                }
                if (resetCounter == 0) {
                    setResetCounter(resetCounter - 1)
                }
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const [updateCustomisedComponent] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const [createCustomisedComponent] = useMutation(
        CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    //end query and mutation

    //start local crud function
    const deleteLocalCustomisedComponentField = async (id: any) => {
        if (+id > 0) {
            const customisedComponentFieldModel =
                new CustomisedComponentFieldModel()
            await customisedComponentFieldModel.delete(id)
        }
    }
    //end local crud function

    getVesselByID(vesselID, (value: any) => {
        setVessel(value)
        loadLogBookConfig()
    })

    //start memoization hook
    const slallFields = useMemo<LogBookConfiguration[]>(() => {
        if (!vessel) {
            return SLALL_LogBookFields
        }

        const filteredFields = getFilteredSlallLogBookFields(vessel)

        if (filteredFields.length === 0) {
            return SLALL_LogBookFields
        }

        return filteredFields
    }, [vessel])

    const filteredTabs = useMemo(() => {
        return tabs.filter(
            (element) =>
                element.title !== 'Crew Welfare' &&
                element.title !== 'Crew Training' &&
                element.componentClass !== 'Engine_LogBookComponent' &&
                element.componentClass !== 'Engineer_LogBookComponent' &&
                element.componentClass !== 'Fuel_LogBookComponent' &&
                element.componentClass !== 'Supernumerary_LogBookComponent',
        )
    }, [tabs])

    const sortedCustomisedLogBookComponents = useMemo(() => {
        if (!logBookConfig) {
            return []
        }

        const elements = [...logBookConfig.customisedLogBookComponents?.nodes]
        // Crew Members must come first before Crew Welfare
        return elements.sort((a: any, b: any) => {
            if (a.componentClass === 'CrewMembers_LogBookComponent') return -1
            if (b.componentClass === 'CrewMembers_LogBookComponent') return 1
            return 0
        })
    }, [logBookConfig])

    const componentsNotInConfig = useMemo(() => {
        const fields = logBookConfig
            ? getComponentsNotInConfig(slallFields, logBookConfig) ?? []
            : []
        return fields
    }, [logBookConfig, slallFields])

    const filterComponentsNotInConfigByTab = useCallback(
        (tab: string): Array<any> => {
            return componentsNotInConfig.filter(
                (component: any) =>
                    tab.replace('Logbook', 'LogBook') ===
                        component.label.replace('Logbook', 'LogBook') ||
                    (component.label === 'Crew Welfare' &&
                        tab === 'Crew Members') ||
                    (tab === 'Engine Reports' &&
                        (component.componentClass ===
                            'Engineer_LogBookComponent' ||
                            component.componentClass ===
                                'Fuel_LogBookComponent')),
            )
        },
        [componentsNotInConfig],
    )

    //end memoization hook

    //------ start use effect section
    useEffect(() => {
        if (!activeTab && Array.isArray(tabs) && tabs.length > 0) {
            setActiveTab(tabs[0].title)
        }
    }, [activeTab, tabs])

    useEffect(() => {
        if (isLoading && vessel) {
            setIsLoading(false)
            setImCrew(isCrew() || false)
            handleSetDailyCheckCategories()
            handleLevelThreeCategories()
        }
    }, [isLoading, vessel])

    //------ end use effect section

    return (
        <ConfigFormContext.Provider
            value={{
                isLoading,
                setIsLoading,
                vessel,
                logBookConfig,
                slallFields,
                filteredTabs,
                activeTab,
                setActiveTab,
                documents,
                setDocuments,
                imCrew: imCrew || false,
                resetCounter,
                setResetCounter,
                updatedFields,
                setUpdatedFields,
                updatedLocalFields,
                setUpdatedLocalFields,
                updateCustomisedComponentField,
                customisedLogBookComponents: sortedCustomisedLogBookComponents,
                levelThreeCategory,
                setLevelThreeCategory,
                dailyCheckCategories,
                dailyCheckCategory,
                levelThreeCategories,
                setDailyCheckCategory,
                editConfigDialog,
                descriptionDialog,
                loadLogBookConfig,
                createCustomisedComponent,
                updateCustomisedComponent,
                activateCustomisedComponent,
                deactivateCustomisedComponent,
                filterComponentsNotInConfigByTab,
            }}>
            {children}
        </ConfigFormContext.Provider>
    )
}

export const useConfigForm = () => {
    const context = useContext(ConfigFormContext)

    if (!context) {
        throw new Error('useConfigForm must be used within ConfigFormProvider')
    }

    return context
}
