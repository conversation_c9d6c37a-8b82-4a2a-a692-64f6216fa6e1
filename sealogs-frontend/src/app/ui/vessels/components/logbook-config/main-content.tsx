'use client'

import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>nt,
    <PERSON><PERSON><PERSON>ist,
    <PERSON><PERSON><PERSON>rigger,
} from '@/components/ui'
import { useConfigForm } from './config-form-context'
import { ConfigCardSkeleton } from './config-card-skeleton'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import { getTabTitle } from '../../actions'
import { LogBookConfigTabContent } from './tab-content'
import { ComponentsNotInConfig } from './components-not-in-config'
import { DescriptionDialog } from './description-dialog'
import { EditConfigDialog } from './edit-config-dialog'
import { IConfigForm } from './types'

export const MainContent = () => {
    const {
        vessel,
        isLoading,
        filteredTabs,
        activeTab,
        setActiveTab,
        filterComponentsNotInConfigByTab,
        descriptionDialog,
        editConfigDialog,
        updateCustomisedComponentField,
    } = useConfigForm()

    const handleUpdateConfigEdit = async (field: any, form: IConfigForm) => {
        document.body.style.cursor = 'wait'
        await updateCustomisedComponentField({
            variables: {
                input: {
                    id: field.id,
                    customisedFieldTitle: form.fieldName,
                    sortOrder: form.sortOrder,
                    description:
                        form.description === '<p><br></p>'
                            ? ''
                            : form.description,
                },
            },
        })

        editConfigDialog.closeDialog()
    }

    if (!vessel && isLoading) {
        return <ConfigCardSkeleton />
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-2xl font-semibold">
                    {vessel?.vesselType?.replaceAll('_', ' ')}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <Tabs
                    value={activeTab}
                    onValueChange={(newTab) => {
                        setActiveTab(newTab)
                    }}>
                    <TabsList>
                        {filteredTabs.map((element) => {
                            return (
                                <TabsTrigger
                                    key={element.title}
                                    value={element.title}>
                                    {getTabTitle(element, SLALL_LogBookFields)}
                                </TabsTrigger>
                            )
                        })}
                    </TabsList>

                    <div className="mt-4 flex flex-col gap-4">
                        {filteredTabs.map((element) => {
                            const notInConfigFields =
                                filterComponentsNotInConfigByTab(element.title)

                            return (
                                <TabsContent value={element.title}>
                                    <LogBookConfigTabContent
                                        tabValue={element.title}
                                    />
                                    {notInConfigFields.length > 0 && (
                                        <ComponentsNotInConfig
                                            fields={notInConfigFields}
                                        />
                                    )}
                                </TabsContent>
                            )
                        })}
                    </div>
                </Tabs>

                <EditConfigDialog
                    title={editConfigDialog.title}
                    field={editConfigDialog.selectedField}
                    isFieldGroup={editConfigDialog.isFieldGroup}
                    defaultValues={editConfigDialog.form}
                    isOpen={editConfigDialog.isOpen}
                    onOpenChange={editConfigDialog.onOpenChange}
                    onSave={(field, form) =>
                        handleUpdateConfigEdit(field, form)
                    }
                />

                <DescriptionDialog
                    open={descriptionDialog.open}
                    onOpenChange={descriptionDialog.onOpenChange}
                    title={descriptionDialog.title}
                    content={descriptionDialog.content}
                />
            </CardContent>
        </Card>
    )
}
