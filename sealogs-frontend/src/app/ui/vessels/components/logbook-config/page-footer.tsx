'use client'

import { FooterWrapper } from '@/components/footer-wrapper'
import { Button } from '@/components/ui'
import {
    ArrowLeft,
    BanIcon,
    FilePlusIcon,
    RotateCcw,
    SaveIcon,
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useConfigForm } from './config-form-context'
import { ResetButton } from './reset-button'
import { useEffect, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { useMutation } from '@apollo/client'
import { UPDATE_CUSTOMISED_LOGBOOK_CONFIG } from '@/app/lib/graphQL/mutation'
import CustomisedLogBookConfigModel from '@/app/offline/models/customisedLogBookConfig'
import { isCategorised } from '../../actions'

export const PageFooter = () => {
    const router = useRouter()
    const { imCrew, isLoading, logBookConfig } = useConfigForm()
    //TODO:: check enable disable button not showing

    console.log(imCrew, isLoading, logBookConfig)

    return (
        <FooterWrapper>
            <Button
                iconLeft={ArrowLeft}
                variant="back"
                onClick={() => {
                    router.back()
                }}>
                Cancel
            </Button>
            {!isLoading && !imCrew && logBookConfig && (
                <>
                    <EnableDisableButton />
                    <AddCustomisedComponentButton />
                </>
            )}
            {!imCrew && <SaveButton />}
        </FooterWrapper>
    )
}

const EnableDisableButton = () => {
    const {
        logBookConfig,
        activeTab,
        deactivateCustomisedComponent,
        activateCustomisedComponent,
        slallFields,
    } = useConfigForm()

    const currentTabComponents =
        logBookConfig.customisedLogBookComponents?.nodes?.filter(
            (component: any) =>
                component.title.replace('Logbook', 'LogBook') ===
                    activeTab.replace('Logbook', 'LogBook') &&
                isCategorised(component, slallFields, logBookConfig),
        )

    if (currentTabComponents.length === 0) {
        return <></>
    }

    return currentTabComponents.map((component: any) => {
        if (component.active) {
            return (
                <div className="flex gap-2">
                    <Button
                        variant="destructive"
                        iconLeft={BanIcon}
                        onClick={() =>
                            deactivateCustomisedComponent(component.id)
                        }>
                        Disable
                    </Button>
                    <ResetButton component={component} />
                </div>
            )
        }

        return (
            <Button
                variant="primary"
                onClick={() => activateCustomisedComponent(component.id)}>
                Enable
            </Button>
        )
    })
}

const AddCustomisedComponentButton = () => {
    const {
        activeTab,
        filterComponentsNotInConfigByTab,
        logBookConfig,
        createCustomisedComponent,
    } = useConfigForm()

    const components = filterComponentsNotInConfigByTab(activeTab)

    if (components.length === 0) {
        return <></>
    }

    return components.map((component) => (
        <Button
            variant="primaryOutline"
            iconLeft={FilePlusIcon}
            onClick={() => {
                document.body.style.cursor = 'wait'
                createCustomisedComponent({
                    variables: {
                        input: {
                            title: component.label,
                            sortOrder: component.sortOrder || 0,
                            category: component.category,
                            customisedLogBookConfigID: logBookConfig.id,
                            componentClass: component.componentClass,
                            active: true,
                        },
                    },
                })
            }}>
            {`Add ${component.label}`}
        </Button>
    ))
}

const SaveButton = () => {
    const { toast } = useToast()
    const { isLoading, documents, logBookConfig, setIsLoading } =
        useConfigForm()

    const [saving, setSaving] = useState(false)
    const [notify, setNotify] = useState(true)

    useEffect(() => {
        if (!isLoading && documents) {
            setNotify(false)
            handleSave(false)
        }
    }, [documents])

    const deleteLocalCustomisedLogBookConfig = async (id: any) => {
        if (+id > 0) {
            const customisedLogBookConfigModel =
                new CustomisedLogBookConfigModel()
            await customisedLogBookConfigModel.delete(id)
        }
    }

    const [updateCustomisedLogBookConfig] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookConfig
                deleteLocalCustomisedLogBookConfig(data.id)
                if (data.id > 0) {
                    if (notify) {
                        toast({
                            title: 'Info!',
                            description: 'Logbook configuration saved',
                        })
                        setSaving(false)
                    }
                    setNotify(true)
                }
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const handleSave = (notify = true) => {
        if (notify) {
            setSaving(true)
            toast({
                title: 'Saving',
                description: 'Logbook configuration saving...',
            })
        } else {
            setNotify(false)
        }
        const policies =
            documents.length > 0
                ? documents?.map((doc: any) => +doc.id).join(',')
                : ''
        document.body.style.cursor = 'wait'
        updateCustomisedLogBookConfig({
            variables: {
                input: {
                    id: logBookConfig.id,
                    policies: policies,
                },
            },
        })
        setIsLoading(true)
    }

    return (
        <Button
            variant="primary"
            onClick={() => handleSave()}
            iconLeft={SaveIcon}
            disabled={saving}>
            {saving ? 'Saving' : 'Save'}
        </Button>
    )
}
